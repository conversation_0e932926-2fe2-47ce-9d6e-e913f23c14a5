name: Setup
description: Setup Node.js and install dependencies
inputs:
  REACT_NATIVE_VERSION:
    description: React Native version to use
    required: false
  NODE_VERSION:
    description: Node version to use # https://github.com/actions/setup-node#supported-version-syntax
    required: false
  IS_EXPO:
    description: Should template use expo
    required: false
    default: 'false'

runs:
  using: composite
  steps:
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ inputs.NODE_VERSION }}

    - name: Display current inputs
      run: echo REACT_NATIVE_VERSION $REACT_NATIVE_VERSION & echo NODE_VERSION $NODE_VERSION & echo IS_EXPO $IS_EXPO
      shell: bash
      env:
        REACT_NATIVE_VERSION: ${{ inputs.REACT_NATIVE_VERSION }}
        NODE_VERSION: ${{ inputs.NODE_VERSION }}
        IS_EXPO: ${{ inputs.IS_EXPO }}

    - name: Cache dependencies
      id: yarn-cache
      uses: actions/cache@v3
      with:
        path: |
          **/node_modules
        key: ${{ runner.os }}-${{ inputs.REACT_NATIVE_VERSION }}-yarn-${{ hashFiles('**/yarn.lock') }}-${{ hashFiles('**/package.json') }}
        restore-keys: |
          ${{ runner.os }}-${{ inputs.REACT_NATIVE_VERSION }}-yarn-${{ hashFiles('**/yarn.lock') }}
          ${{ runner.os }}-${{ inputs.REACT_NATIVE_VERSION }}-yarn-

    - name: Install dependencies
      if: steps.yarn-cache.outputs.cache-hit != 'true'
      run: |
        yarn install --cwd example --no-immutable
        yarn install
      shell: bash
