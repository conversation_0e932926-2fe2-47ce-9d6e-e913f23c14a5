// Generated by Apple Swift version 5.8.1 (swiftlang-*********.5 clang-1403.***********)
#ifndef MULTIPLATFORMBLEADAPTER_SWIFT_H
#define MULTIPLATFORMBLEADAPTER_SWIFT_H
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgcc-compat"

#if !defined(__has_include)
# define __has_include(x) 0
#endif
#if !defined(__has_attribute)
# define __has_attribute(x) 0
#endif
#if !defined(__has_feature)
# define __has_feature(x) 0
#endif
#if !defined(__has_warning)
# define __has_warning(x) 0
#endif

#if __has_include(<swift/objc-prologue.h>)
# include <swift/objc-prologue.h>
#endif

#pragma clang diagnostic ignored "-Wauto-import"
#if defined(__OBJC__)
#include <Foundation/Foundation.h>
#endif
#if defined(__cplusplus)
#include <cstdint>
#include <cstddef>
#include <cstdbool>
#include <cstring>
#include <stdlib.h>
#include <new>
#include <type_traits>
#else
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <string.h>
#endif
#if defined(__cplusplus)
#if __has_include(<ptrauth.h>)
# include <ptrauth.h>
#else
# ifndef __ptrauth_swift_value_witness_function_pointer
#  define __ptrauth_swift_value_witness_function_pointer(x)
# endif
#endif
#endif

#if !defined(SWIFT_TYPEDEFS)
# define SWIFT_TYPEDEFS 1
# if __has_include(<uchar.h>)
#  include <uchar.h>
# elif !defined(__cplusplus)
typedef uint_least16_t char16_t;
typedef uint_least32_t char32_t;
# endif
typedef float swift_float2  __attribute__((__ext_vector_type__(2)));
typedef float swift_float3  __attribute__((__ext_vector_type__(3)));
typedef float swift_float4  __attribute__((__ext_vector_type__(4)));
typedef double swift_double2  __attribute__((__ext_vector_type__(2)));
typedef double swift_double3  __attribute__((__ext_vector_type__(3)));
typedef double swift_double4  __attribute__((__ext_vector_type__(4)));
typedef int swift_int2  __attribute__((__ext_vector_type__(2)));
typedef int swift_int3  __attribute__((__ext_vector_type__(3)));
typedef int swift_int4  __attribute__((__ext_vector_type__(4)));
typedef unsigned int swift_uint2  __attribute__((__ext_vector_type__(2)));
typedef unsigned int swift_uint3  __attribute__((__ext_vector_type__(3)));
typedef unsigned int swift_uint4  __attribute__((__ext_vector_type__(4)));
#endif

#if !defined(SWIFT_PASTE)
# define SWIFT_PASTE_HELPER(x, y) x##y
# define SWIFT_PASTE(x, y) SWIFT_PASTE_HELPER(x, y)
#endif
#if !defined(SWIFT_METATYPE)
# define SWIFT_METATYPE(X) Class
#endif
#if !defined(SWIFT_CLASS_PROPERTY)
# if __has_feature(objc_class_property)
#  define SWIFT_CLASS_PROPERTY(...) __VA_ARGS__
# else
#  define SWIFT_CLASS_PROPERTY(...) 
# endif
#endif
#if !defined(SWIFT_RUNTIME_NAME)
# if __has_attribute(objc_runtime_name)
#  define SWIFT_RUNTIME_NAME(X) __attribute__((objc_runtime_name(X)))
# else
#  define SWIFT_RUNTIME_NAME(X) 
# endif
#endif
#if !defined(SWIFT_COMPILE_NAME)
# if __has_attribute(swift_name)
#  define SWIFT_COMPILE_NAME(X) __attribute__((swift_name(X)))
# else
#  define SWIFT_COMPILE_NAME(X) 
# endif
#endif
#if !defined(SWIFT_METHOD_FAMILY)
# if __has_attribute(objc_method_family)
#  define SWIFT_METHOD_FAMILY(X) __attribute__((objc_method_family(X)))
# else
#  define SWIFT_METHOD_FAMILY(X) 
# endif
#endif
#if !defined(SWIFT_NOESCAPE)
# if __has_attribute(noescape)
#  define SWIFT_NOESCAPE __attribute__((noescape))
# else
#  define SWIFT_NOESCAPE 
# endif
#endif
#if !defined(SWIFT_RELEASES_ARGUMENT)
# if __has_attribute(ns_consumed)
#  define SWIFT_RELEASES_ARGUMENT __attribute__((ns_consumed))
# else
#  define SWIFT_RELEASES_ARGUMENT 
# endif
#endif
#if !defined(SWIFT_WARN_UNUSED_RESULT)
# if __has_attribute(warn_unused_result)
#  define SWIFT_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
# else
#  define SWIFT_WARN_UNUSED_RESULT 
# endif
#endif
#if !defined(SWIFT_NORETURN)
# if __has_attribute(noreturn)
#  define SWIFT_NORETURN __attribute__((noreturn))
# else
#  define SWIFT_NORETURN 
# endif
#endif
#if !defined(SWIFT_CLASS_EXTRA)
# define SWIFT_CLASS_EXTRA 
#endif
#if !defined(SWIFT_PROTOCOL_EXTRA)
# define SWIFT_PROTOCOL_EXTRA 
#endif
#if !defined(SWIFT_ENUM_EXTRA)
# define SWIFT_ENUM_EXTRA 
#endif
#if !defined(SWIFT_CLASS)
# if __has_attribute(objc_subclassing_restricted)
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# else
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# endif
#endif
#if !defined(SWIFT_RESILIENT_CLASS)
# if __has_attribute(objc_class_stub)
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME) __attribute__((objc_class_stub))
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_class_stub)) SWIFT_CLASS_NAMED(SWIFT_NAME)
# else
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME)
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) SWIFT_CLASS_NAMED(SWIFT_NAME)
# endif
#endif
#if !defined(SWIFT_PROTOCOL)
# define SWIFT_PROTOCOL(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
# define SWIFT_PROTOCOL_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
#endif
#if !defined(SWIFT_EXTENSION)
# define SWIFT_EXTENSION(M) SWIFT_PASTE(M##_Swift_, __LINE__)
#endif
#if !defined(OBJC_DESIGNATED_INITIALIZER)
# if __has_attribute(objc_designated_initializer)
#  define OBJC_DESIGNATED_INITIALIZER __attribute__((objc_designated_initializer))
# else
#  define OBJC_DESIGNATED_INITIALIZER 
# endif
#endif
#if !defined(SWIFT_ENUM_ATTR)
# if __has_attribute(enum_extensibility)
#  define SWIFT_ENUM_ATTR(_extensibility) __attribute__((enum_extensibility(_extensibility)))
# else
#  define SWIFT_ENUM_ATTR(_extensibility) 
# endif
#endif
#if !defined(SWIFT_ENUM)
# define SWIFT_ENUM(_type, _name, _extensibility) enum _name : _type _name; enum SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# if __has_feature(generalized_swift_name)
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) enum _name : _type _name SWIFT_COMPILE_NAME(SWIFT_NAME); enum SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# else
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) SWIFT_ENUM(_type, _name, _extensibility)
# endif
#endif
#if !defined(SWIFT_UNAVAILABLE)
# define SWIFT_UNAVAILABLE __attribute__((unavailable))
#endif
#if !defined(SWIFT_UNAVAILABLE_MSG)
# define SWIFT_UNAVAILABLE_MSG(msg) __attribute__((unavailable(msg)))
#endif
#if !defined(SWIFT_AVAILABILITY)
# define SWIFT_AVAILABILITY(plat, ...) __attribute__((availability(plat, __VA_ARGS__)))
#endif
#if !defined(SWIFT_WEAK_IMPORT)
# define SWIFT_WEAK_IMPORT __attribute__((weak_import))
#endif
#if !defined(SWIFT_DEPRECATED)
# define SWIFT_DEPRECATED __attribute__((deprecated))
#endif
#if !defined(SWIFT_DEPRECATED_MSG)
# define SWIFT_DEPRECATED_MSG(...) __attribute__((deprecated(__VA_ARGS__)))
#endif
#if !defined(SWIFT_DEPRECATED_OBJC)
# if __has_feature(attribute_diagnose_if_objc)
#  define SWIFT_DEPRECATED_OBJC(Msg) __attribute__((diagnose_if(1, Msg, "warning")))
# else
#  define SWIFT_DEPRECATED_OBJC(Msg) SWIFT_DEPRECATED_MSG(Msg)
# endif
#endif
#if defined(__OBJC__)
#if !defined(IBSegueAction)
# define IBSegueAction 
#endif
#endif
#if !defined(SWIFT_EXTERN)
# if defined(__cplusplus)
#  define SWIFT_EXTERN extern "C"
# else
#  define SWIFT_EXTERN extern
# endif
#endif
#if !defined(SWIFT_CALL)
# define SWIFT_CALL __attribute__((swiftcall))
#endif
#if !defined(SWIFT_INDIRECT_RESULT)
# define SWIFT_INDIRECT_RESULT __attribute__((swift_indirect_result))
#endif
#if !defined(SWIFT_CONTEXT)
# define SWIFT_CONTEXT __attribute__((swift_context))
#endif
#if !defined(SWIFT_ERROR_RESULT)
# define SWIFT_ERROR_RESULT __attribute__((swift_error_result))
#endif
#if defined(__cplusplus)
# define SWIFT_NOEXCEPT noexcept
#else
# define SWIFT_NOEXCEPT 
#endif
#if defined(_WIN32)
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL __declspec(dllimport)
#endif
#else
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL 
#endif
#endif
#if defined(__OBJC__)
#if __has_feature(objc_modules)
#if __has_warning("-Watimport-in-framework-header")
#pragma clang diagnostic ignored "-Watimport-in-framework-header"
#endif
@import Dispatch;
@import Foundation;
@import ObjectiveC;
#endif

#endif
#pragma clang diagnostic ignored "-Wproperty-attribute-mismatch"
#pragma clang diagnostic ignored "-Wduplicate-method-arg"
#if __has_warning("-Wpragma-clang-attribute")
# pragma clang diagnostic ignored "-Wpragma-clang-attribute"
#endif
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma clang diagnostic ignored "-Wnullability"
#pragma clang diagnostic ignored "-Wdollar-in-identifier-extension"

#if __has_attribute(external_source_symbol)
# pragma push_macro("any")
# undef any
# pragma clang attribute push(__attribute__((external_source_symbol(language="Swift", defined_in="MultiplatformBleAdapter",generated_declaration))), apply_to=any(function,enum,objc_interface,objc_category,objc_protocol))
# pragma pop_macro("any")
#endif

#if defined(__OBJC__)
@protocol BleClientManagerDelegate;
@class NSString;
@class NSError;

SWIFT_PROTOCOL("_TtP23MultiplatformBleAdapter10BleAdapter_")
@protocol BleAdapter
@property (nonatomic, strong) id <BleClientManagerDelegate> _Nullable delegate;
- (nonnull instancetype)initWithQueue:(dispatch_queue_t _Nonnull)queue restoreIdentifierKey:(NSString * _Nullable)restoreIdentifierKey;
- (void)invalidate;
- (void)cancelTransaction:(NSString * _Nonnull)transactionId;
- (void)setLogLevel:(NSString * _Nonnull)logLevel;
- (void)logLevel:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)enable:(NSString * _Nonnull)transactionId resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)disable:(NSString * _Nonnull)transactionId resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)state:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)startDeviceScan:(NSArray<NSString *> * _Nullable)filteredUUIDs options:(NSDictionary<NSString *, id> * _Nullable)options;
- (void)stopDeviceScan;
- (void)readRSSIForDevice:(NSString * _Nonnull)deviceIdentifier transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)requestMTUForDevice:(NSString * _Nonnull)deviceIdentifier mtu:(NSInteger)mtu transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)requestConnectionPriorityForDevice:(NSString * _Nonnull)deviceIdentifier connectionPriority:(NSInteger)connectionPriority transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)devices:(NSArray<NSString *> * _Nonnull)deviceIdentifiers resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)connectedDevices:(NSArray<NSString *> * _Nonnull)serviceUUIDs resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)connectToDevice:(NSString * _Nonnull)deviceIdentifier options:(NSDictionary<NSString *, id> * _Nullable)options resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)cancelDeviceConnection:(NSString * _Nonnull)deviceIdentifier resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)isDeviceConnected:(NSString * _Nonnull)deviceIdentifier resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)discoverAllServicesAndCharacteristicsForDevice:(NSString * _Nonnull)deviceIdentifier transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)servicesForDevice:(NSString * _Nonnull)deviceIdentifier resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)characteristicsForDevice:(NSString * _Nonnull)deviceIdentifier serviceUUID:(NSString * _Nonnull)serviceUUID resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)characteristicsForService:(double)serviceIdentifier resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)descriptorsForDevice:(NSString * _Nonnull)deviceIdentifier serviceUUID:(NSString * _Nonnull)serviceUUID characteristicUUID:(NSString * _Nonnull)characteristicUUID resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)descriptorsForService:(double)serviceIdentifier characteristicUUID:(NSString * _Nonnull)characteristicUUID resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)descriptorsForCharacteristic:(double)characteristicIdentifier resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)readCharacteristicForDevice:(NSString * _Nonnull)deviceIdentifier serviceUUID:(NSString * _Nonnull)serviceUUID characteristicUUID:(NSString * _Nonnull)characteristicUUID transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)readCharacteristicForService:(double)serviceIdentifier characteristicUUID:(NSString * _Nonnull)characteristicUUID transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)readCharacteristic:(double)characteristicIdentifier transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)writeCharacteristicForDevice:(NSString * _Nonnull)deviceIdentifier serviceUUID:(NSString * _Nonnull)serviceUUID characteristicUUID:(NSString * _Nonnull)characteristicUUID valueBase64:(NSString * _Nonnull)valueBase64 response:(BOOL)response transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)writeCharacteristicForService:(double)serviceIdentifier characteristicUUID:(NSString * _Nonnull)characteristicUUID valueBase64:(NSString * _Nonnull)valueBase64 response:(BOOL)response transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)writeCharacteristic:(double)characteristicIdentifier valueBase64:(NSString * _Nonnull)valueBase64 response:(BOOL)response transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)monitorCharacteristicForDevice:(NSString * _Nonnull)deviceIdentifier serviceUUID:(NSString * _Nonnull)serviceUUID characteristicUUID:(NSString * _Nonnull)characteristicUUID transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)monitorCharacteristicForService:(double)serviceIdentifier characteristicUUID:(NSString * _Nonnull)characteristicUUID transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)monitorCharacteristic:(double)characteristicIdentifier transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)readDescriptorForDevice:(NSString * _Nonnull)deviceIdentifier serviceUUID:(NSString * _Nonnull)serviceUUID characteristicUUID:(NSString * _Nonnull)characteristicUUID descriptorUUID:(NSString * _Nonnull)descriptorUUID transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)readDescriptorForService:(double)serviceId characteristicUUID:(NSString * _Nonnull)characteristicUUID descriptorUUID:(NSString * _Nonnull)descriptorUUID transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)readDescriptorForCharacteristic:(double)characteristicID descriptorUUID:(NSString * _Nonnull)descriptorUUID transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)readDescriptor:(double)descriptorID transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)writeDescriptorForDevice:(NSString * _Nonnull)deviceIdentifier serviceUUID:(NSString * _Nonnull)serviceUUID characteristicUUID:(NSString * _Nonnull)characteristicUUID descriptorUUID:(NSString * _Nonnull)descriptorUUID valueBase64:(NSString * _Nonnull)valueBase64 transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)writeDescriptorForService:(double)serviceID characteristicUUID:(NSString * _Nonnull)characteristicUUID descriptorUUID:(NSString * _Nonnull)descriptorUUID valueBase64:(NSString * _Nonnull)valueBase64 transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)writeDescriptorForCharacteristic:(double)characteristicID descriptorUUID:(NSString * _Nonnull)descriptorUUID valueBase64:(NSString * _Nonnull)valueBase64 transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)writeDescriptor:(double)descriptorID valueBase64:(NSString * _Nonnull)valueBase64 transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
@end


SWIFT_CLASS("_TtC23MultiplatformBleAdapter17BleAdapterFactory")
@interface BleAdapterFactory : NSObject
+ (id <BleAdapter> _Nonnull)getNewAdapterWithQueue:(dispatch_queue_t _Nonnull)queue restoreIdentifierKey:(NSString * _Nullable)restoreIdentifierKey SWIFT_WARN_UNUSED_RESULT;
+ (void)setBleAdapterCreator:(id <BleAdapter> _Nonnull (^ _Nonnull)(dispatch_queue_t _Nonnull, NSString * _Nullable))bleAdapterCreator;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


SWIFT_CLASS("_TtC23MultiplatformBleAdapter16BleClientManager")
@interface BleClientManager : NSObject
@property (nonatomic, strong) id <BleClientManagerDelegate> _Nullable delegate;
- (nonnull instancetype)initWithQueue:(dispatch_queue_t _Nonnull)queue restoreIdentifierKey:(NSString * _Nullable)restoreIdentifierKey OBJC_DESIGNATED_INITIALIZER;
- (void)invalidate;
- (void)cancelTransaction:(NSString * _Nonnull)transactionId;
- (void)setLogLevel:(NSString * _Nonnull)logLevel;
- (void)logLevel:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)enable:(NSString * _Nonnull)transactionId resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)disable:(NSString * _Nonnull)transactionId resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)state:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)startDeviceScan:(NSArray<NSString *> * _Nullable)filteredUUIDs options:(NSDictionary<NSString *, id> * _Nullable)options;
- (void)stopDeviceScan;
- (void)readRSSIForDevice:(NSString * _Nonnull)deviceIdentifier transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)requestMTUForDevice:(NSString * _Nonnull)deviceIdentifier mtu:(NSInteger)mtu transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)requestConnectionPriorityForDevice:(NSString * _Nonnull)deviceIdentifier connectionPriority:(NSInteger)connectionPriority transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)devices:(NSArray<NSString *> * _Nonnull)deviceIdentifiers resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)connectedDevices:(NSArray<NSString *> * _Nonnull)serviceUUIDs resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)connectToDevice:(NSString * _Nonnull)deviceIdentifier options:(NSDictionary<NSString *, id> * _Nullable)options resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)cancelDeviceConnection:(NSString * _Nonnull)deviceIdentifier resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)isDeviceConnected:(NSString * _Nonnull)deviceIdentifier resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)discoverAllServicesAndCharacteristicsForDevice:(NSString * _Nonnull)deviceIdentifier transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)servicesForDevice:(NSString * _Nonnull)deviceIdentifier resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)characteristicsForDevice:(NSString * _Nonnull)deviceIdentifier serviceUUID:(NSString * _Nonnull)serviceUUID resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)characteristicsForService:(double)serviceIdentifier resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)descriptorsForDevice:(NSString * _Nonnull)deviceIdentifier serviceUUID:(NSString * _Nonnull)serviceUUID characteristicUUID:(NSString * _Nonnull)characteristicUUID resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)descriptorsForService:(double)serviceIdentifier characteristicUUID:(NSString * _Nonnull)characteristicUUID resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)descriptorsForCharacteristic:(double)characteristicIdentifier resolve:(SWIFT_NOESCAPE void (^ _Nonnull)(id _Nullable))resolve reject:(SWIFT_NOESCAPE void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)readCharacteristicForDevice:(NSString * _Nonnull)deviceIdentifier serviceUUID:(NSString * _Nonnull)serviceUUID characteristicUUID:(NSString * _Nonnull)characteristicUUID transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)readCharacteristicForService:(double)serviceIdentifier characteristicUUID:(NSString * _Nonnull)characteristicUUID transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)readCharacteristic:(double)characteristicIdentifier transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)writeCharacteristicForDevice:(NSString * _Nonnull)deviceIdentifier serviceUUID:(NSString * _Nonnull)serviceUUID characteristicUUID:(NSString * _Nonnull)characteristicUUID valueBase64:(NSString * _Nonnull)valueBase64 response:(BOOL)response transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)writeCharacteristicForService:(double)serviceIdentifier characteristicUUID:(NSString * _Nonnull)characteristicUUID valueBase64:(NSString * _Nonnull)valueBase64 response:(BOOL)response transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)writeCharacteristic:(double)characteristicIdentifier valueBase64:(NSString * _Nonnull)valueBase64 response:(BOOL)response transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)monitorCharacteristicForDevice:(NSString * _Nonnull)deviceIdentifier serviceUUID:(NSString * _Nonnull)serviceUUID characteristicUUID:(NSString * _Nonnull)characteristicUUID transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)monitorCharacteristicForService:(double)serviceIdentifier characteristicUUID:(NSString * _Nonnull)characteristicUUID transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)monitorCharacteristic:(double)characteristicIdentifier transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)readDescriptorForDevice:(NSString * _Nonnull)deviceIdentifier serviceUUID:(NSString * _Nonnull)serviceUUID characteristicUUID:(NSString * _Nonnull)characteristicUUID descriptorUUID:(NSString * _Nonnull)descriptorUUID transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)readDescriptorForService:(double)serviceId characteristicUUID:(NSString * _Nonnull)characteristicUUID descriptorUUID:(NSString * _Nonnull)descriptorUUID transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)readDescriptorForCharacteristic:(double)characteristicID descriptorUUID:(NSString * _Nonnull)descriptorUUID transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)readDescriptor:(double)descriptorID transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)writeDescriptorForDevice:(NSString * _Nonnull)deviceIdentifier serviceUUID:(NSString * _Nonnull)serviceUUID characteristicUUID:(NSString * _Nonnull)characteristicUUID descriptorUUID:(NSString * _Nonnull)descriptorUUID valueBase64:(NSString * _Nonnull)valueBase64 transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)writeDescriptorForService:(double)serviceID characteristicUUID:(NSString * _Nonnull)characteristicUUID descriptorUUID:(NSString * _Nonnull)descriptorUUID valueBase64:(NSString * _Nonnull)valueBase64 transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)writeDescriptorForCharacteristic:(double)characteristicID descriptorUUID:(NSString * _Nonnull)descriptorUUID valueBase64:(NSString * _Nonnull)valueBase64 transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (void)writeDescriptor:(double)descriptorID valueBase64:(NSString * _Nonnull)valueBase64 transactionId:(NSString * _Nonnull)transactionId resolve:(void (^ _Nonnull)(id _Nullable))resolve reject:(void (^ _Nonnull)(NSString * _Nullable, NSString * _Nullable, NSError * _Nullable))reject;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


@interface BleClientManager (SWIFT_EXTENSION(MultiplatformBleAdapter)) <BleAdapter>
@end


SWIFT_PROTOCOL("_TtP23MultiplatformBleAdapter24BleClientManagerDelegate_")
@protocol BleClientManagerDelegate
- (void)dispatchEvent:(NSString * _Nonnull)name value:(id _Nonnull)value;
@end


SWIFT_CLASS("_TtC23MultiplatformBleAdapter8BleEvent")
@interface BleEvent : NSObject
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull scanEvent;)
+ (NSString * _Nonnull)scanEvent SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull readEvent;)
+ (NSString * _Nonnull)readEvent SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull stateChangeEvent;)
+ (NSString * _Nonnull)stateChangeEvent SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull restoreStateEvent;)
+ (NSString * _Nonnull)restoreStateEvent SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull disconnectionEvent;)
+ (NSString * _Nonnull)disconnectionEvent SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull connectingEvent;)
+ (NSString * _Nonnull)connectingEvent SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull connectedEvent;)
+ (NSString * _Nonnull)connectedEvent SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSArray<NSString *> * _Nonnull events;)
+ (NSArray<NSString *> * _Nonnull)events SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end













#endif
#if defined(__cplusplus)
#endif
#if __has_attribute(external_source_symbol)
# pragma clang attribute pop
#endif
#pragma clang diagnostic pop
#endif
