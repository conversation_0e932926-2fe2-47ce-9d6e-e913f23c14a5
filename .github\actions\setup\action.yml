name: Setup
description: Setup Node.js and install dependencies

runs:
  using: composite
  steps:
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version-file: .nvmrc

    - name: Cache dependencies
      id: yarn-cache
      uses: actions/cache@v3
      with:
        path: |
          **/node_modules
        key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}-${{ hashFiles('**/package.json') }}
        restore-keys: |
          ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          ${{ runner.os }}-yarn-

    - name: Install dependencies
      if: steps.yarn-cache.outputs.cache-hit != 'true'
      run: |
        yarn install --cwd example --frozen-lockfile
        yarn install --frozen-lockfile
      shell: bash
