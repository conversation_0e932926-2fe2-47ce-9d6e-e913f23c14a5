{"root": true, "parser": "@typescript-eslint/parser", "extends": ["@react-native-community", "prettier"], "env": {"react-native/react-native": true, "es2020": true}, "overrides": [{"files": ["src/**", "__tests__/**", "integration-tests/**"], "parser": "hermes-es<PERSON>", "extends": ["plugin:react/recommended", "plugin:flowtype/recommended", "plugin:prettier/recommended", "@react-native-community", "eslint:recommended", "plugin:jest/recommended", "plugin:ft-flow/recommended"], "plugins": ["ft-flow", "react", "react-native", "flowtype", "jest", "prettier"], "rules": {"ft-flow/define-flow-type": "off", "ft-flow/use-flow-type": "off", "flowtype/define-flow-type": "off", "flowtype/no-types-missing-file-annotation": 0, "semi": ["error", "never"], "comma-dangle": ["error", "never"], "react/display-name": 0, "ft-flow/no-types-missing-file-annotation": 0, "jest/no-export": 0}, "globals": {"$Keys": true, "$Values": true}}, {"files": ["*.tsx", "*.ts", "*.d.ts"], "parser": "@typescript-eslint/parser", "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react-hooks/recommended", "plugin:react/recommended", "airbnb", "airbnb-typescript", "prettier"], "parserOptions": {"project": ["./tsconfig.json"]}, "plugins": ["react-refresh", "@typescript-eslint"], "rules": {"import/prefer-default-export": "off", "import/no-default-export": 2, "@typescript-eslint/no-use-before-define": "off", "no-promise-executor-return": "off", "no-bitwise": "off", "no-param-reassign": "off", "react/require-default-props": "off", "no-continue": "off", "no-constant-condition": "off", "no-await-in-loop": "off", "react-native/no-inline-styles": "off", "@typescript-eslint/no-namespace": "off", "@typescript-eslint/no-empty-interface": "off", "react/jsx-props-no-spreading": "off", "class-methods-use-this": "off", "eslint-comments/no-unused-disable": "off", "no-console": ["error", {"allow": ["info", "warn", "error"]}], "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}]}, "env": {"es2020": true}}, {"files": ["*.d.ts"], "rules": {"max-classes-per-file": "off", "prettier/prettier": "off"}}, {"files": ["plugin/**"], "rules": {"no-underscore-dangle": 0, "@typescript-eslint/naming-convention": 0, "import/no-default-export": 0, "@typescript-eslint/no-shadow": 0, "global-require": 0, "@typescript-eslint/no-var-requires": 0, "import/no-extraneous-dependencies": "off"}}]}