{"name": "example-expo", "version": "1.0.0", "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"expo": "~51.0.14", "expo-status-bar": "~1.12.1", "react": "18.2.0", "react-native": "0.74.2", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.26", "react-native-base64": "^0.2.1", "react-native-safe-area-context": "^4.10.1", "react-native-screens": "^3.31.1", "react-native-toast-message": "^2.1.6", "styled-components": "^6.0.7", "react-native-ble-plx": "../"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "typescript": "^5.1.3", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@types/react-native-base64": "^0.2.0", "@react-native/metro-config": "0.74.83", "babel-plugin-module-resolver": "^5.0.0", "metro-react-native-babel-preset": "0.76.8", "@react-native/babel-preset": "0.74.83", "@react-native/typescript-config": "0.74.83"}, "private": true}