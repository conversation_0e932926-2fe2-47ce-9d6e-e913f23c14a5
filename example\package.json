{"name": "BlePlxExample", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "pods": "pod-install --quiet"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "react": "18.3.1", "react-native": "0.77.0", "react-native-base64": "^0.2.1", "react-native-safe-area-context": "^5.1.0", "react-native-screens": "^4.5.0", "react-native-toast-message": "^2.1.6", "styled-components": "^6.0.7"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.77.0", "@react-native/eslint-config": "0.77.0", "@react-native/metro-config": "0.77.0", "@react-native/typescript-config": "0.77.0", "@types/react-native-base64": "^0.2.0", "babel-plugin-module-resolver": "^5.0.0", "metro-react-native-babel-preset": "0.76.8"}, "engines": {"node": ">=18"}}