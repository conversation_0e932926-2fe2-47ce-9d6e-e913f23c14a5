{"name": "react-native-ble-plx", "version": "3.5.0", "packageManager": "yarn@1.22.22", "description": "React Native Bluetooth Low Energy library", "main": "src/index", "module": "src/index", "types": "src/index.d.ts", "react-native": "src/index", "source": "src/index", "files": ["src", "lib", "android", "ios", "cpp", "plugin/build", "app.plugin.js", "*.podsp<PERSON>", "!lib/typescript/example", "!lib/typescript/example-expo", "!ios/build", "!android/build", "!android/gradle", "!android/gradlew", "!android/gradlew.bat", "!android/local.properties", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__", "!**/.*"], "scripts": {"build": "babel src/ -d lib/", "test": "yarn test:package ; yarn test:example", "test:package": "jest --config jest.config.js --passWithNoTests", "test:example": "jest --config example/jest.config.js --passWithNoTests", "docs": "documentation build src/index.js -o docs --config documentation.yml -f html", "typecheck": "tsc --noEmit", "lint": "flow && eslint \"**/*.{js,ts,tsx}\" && documentation lint index.js && yarn typecheck", "prepack": "bob build", "release": "release-it", "example": "yarn --cwd example", "build:android": "cd example/android && ./gradlew assembleDebug --no-daemon --console=plain -PreactNativeArchitectures=arm64-v8a", "test:android": "cd example/android && ./gradlew assembleDebug --no-daemon --console=plain -PreactNativeArchitectures=arm64-v8a", "build:ios": "cd example/ios && xcodebuild -workspace BlePlxExample.xcworkspace -scheme BlePlxExample -configuration Debug -sdk iphonesimulator CC=clang CPLUSPLUS=clang++ LD=clang LDPLUSPLUS=clang++ GCC_OPTIMIZATION_LEVEL=0 GCC_PRECOMPILE_PREFIX_HEADER=YES ASSETCATALOG_COMPILER_OPTIMIZATION=time DEBUG_INFORMATION_FORMAT=dwarf COMPILER_INDEX_STORE_ENABLE=NO", "test:ios": "cd example/ios && xcodebuild -workspace BlePlxExample.xcworkspace -scheme BlePlxExample -configuration Debug -sdk iphonesimulator ASSETCATALOG_COMPILER_OPTIMIZATION=time DEBUG_INFORMATION_FORMAT=dwarf COMPILER_INDEX_STORE_ENABLE=NO", "test:expo": "cd example-expo && yarn install && npx expo prebuild", "bootstrap": "yarn example && yarn install && yarn example pods", "clean": "del-cli android/build example/android/build example/android/app/build example/ios/build plugin/build lib", "build:plugin": "tsc --build plugin", "clean:plugin": "expo-module clean plugin", "test:plugin": "jest --config plugin/jest.config.js", "prepare": "npm run clean:plugin && npm run build:plugin", "lint:plugin": "eslint plugin/src/*"}, "keywords": ["react-native", "ios", "android", "React", "Native", "Bluetooth", "Low", "Energy", "BLE"], "repository": "https://github.com/dotintent/react-native-ble-plx.git", "author": "dotintent (https://withintent.com/)", "license": "MIT", "bugs": {"url": "https://github.com/dotintent/react-native-ble-plx/issues"}, "homepage": "https://github.com/dotintent/react-native-ble-plx#readme", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@babel/cli": "^7.25.9", "@babel/core": "^7.25.2", "@babel/preset-flow": "^7.25.9", "@commitlint/config-conventional": "^17.0.2", "@evilmartians/lefthook": "^1.5.0", "@react-native-community/eslint-config": "^3.0.2", "@react-native/eslint-config": "^0.73.1", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.26", "@release-it/conventional-changelog": "^5.0.0", "@types/jest": "^29.5.5", "@types/react": "^18.2.44", "@types/react-native": "0.70.0", "@types/react-native-base64": "^0.2.2", "@typescript-eslint/parser": "^5.62.0", "babel-plugin-syntax-hermes-parser": "^0.15.1", "commitlint": "^17.0.2", "del-cli": "^5.1.0", "documentation": "12.3.0", "eslint": "^8.51.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-ft-flow": "^2.0.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react-refresh": "^0.4.3", "expo-module-scripts": "^3.1.0", "flow-bin": "^0.259.1", "hermes-eslint": "^0.15.1", "jest": "^29.7.0", "pod-install": "^0.1.0", "prettier": "^3.0.3", "react": "18.3.1", "react-native": "0.77.0", "react-native-base64": "^0.2.1", "react-native-builder-bob": "^0.20.0", "react-native-safe-area-context": "^4.10.5", "react-native-toast-message": "^2.2.0", "release-it": "^17.3.0", "styled-components": "^6.1.11", "turbo": "^1.10.7", "typescript": "^5.2.2"}, "resolutions": {"@types/react": "^18.2.44"}, "peerDependencies": {"react": "*", "react-native": "*"}, "engines": {"node": ">= 18.0.0"}, "jest": {"preset": "react-native", "modulePathIgnorePatterns": ["<rootDir>/integration-tests", "<rootDir>/example/node_modules", "<rootDir>/example-expo/node_modules", "<rootDir>/lib/"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular"}}}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", ["typescript", {"project": "tsconfig.build.json"}]]}}