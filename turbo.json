{"$schema": "https://turbo.build/schema.json", "pipeline": {"build:android": {"inputs": ["package.json", "android", "!android/build", "src/*.ts", "src/*.tsx", "example/package.json", "example/android", "!example/android/.gradle", "!example/android/build", "!example/android/app/build"], "outputs": []}, "test:android": {"inputs": ["package.json", "android", "!android/build", "src/*.ts", "src/*.tsx", "test_project/package.json", "test_project/android", "!test_project/android/.gradle", "!test_project/android/build", "!test_project/android/app/build"], "outputs": []}, "build:ios": {"inputs": ["package.json", "*.podsp<PERSON>", "ios", "src/*.ts", "src/*.tsx", "example/package.json", "example/ios", "!example/ios/build", "!example/ios/Pods"], "outputs": []}, "test:ios": {"inputs": ["package.json", "*.podsp<PERSON>", "ios", "src/*.ts", "src/*.tsx", "test_project/package.json", "test_project/ios", "!test_project/ios/build", "!test_project/ios/Pods"], "outputs": []}}}