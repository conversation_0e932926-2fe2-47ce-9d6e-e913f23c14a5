#!/bin/bash

set -eo pipefail

PROJECT_PATH=$1

pushd "$PROJECT_PATH"
  SIMULATOR_PARAMS='platform=iOS Simulator,name=iPhone 11,OS=13.4.1'
  npx flow check
  npm start &
  sleep 5
  curl -s http://localhost:8081/index.bundle\?platform\=ios\&dev\=true\&minify\=false > /dev/null
  if [ -d ios/Setup.xcworkspace ]; then
    echo "Building XCode workspace..."
    xcodebuild test -scheme Setup -workspace ios/Setup.xcworkspace -quiet -UseModernBuildSystem=YES -destination "$SIMULATOR_PARAMS" > /dev/null
  else
    echo "Building XCode project..."
    xcodebuild test -scheme Setup -project ios/Setup.xcodeproj -quiet -UseModernBuildSystem=YES -destination "$SIMULATOR_PARAMS" > /dev/null
  fi
popd