[ignore]
; We fork some components by platform
.*/*[.]android.js

; Ignore "BUCK" generated dirs
<PROJECT_ROOT>/\.buckd/

; Ignore polyfills
node_modules/react-native/Libraries/polyfills/.*

; These should not be required directly
; require from fbjs/lib instead: require('fbjs/lib/warning')
node_modules/warning/.*

; Flow doesn't support platforms
.*/Libraries/Utilities/LoadingView.js

; Integration tests
.*/integration-tests/.*

; Example app
<PROJECT_ROOT>/example/.*
<PROJECT_ROOT>/example-expo/.*


; Node modules errors
<PROJECT_ROOT>/node_modules/.*
!<PROJECT_ROOT>/node_modules/react-native/
<PROJECT_ROOT>/node_modules/react-native/Libraries/DevToolsSettings/DevToolsSettingsManager.d.ts

; Output files
<PROJECT_ROOT>/lib/.*
<PROJECT_ROOT>/test_project/.*

[untyped]
.*/node_modules/@react-native-community/cli/.*/.*

[include]

[libs]
node_modules/react-native/interface.js
node_modules/react-native/Libraries/react-native/react-native-interface.js
node_modules/react-native/flow/
flow/

[options]
emoji=true

module.file_ext=.js
module.file_ext=.ts
module.file_ext=.json
module.file_ext=.ios.js

exact_by_default=true

module.name_mapper='^react-native/\(.*\)$' -> '<PROJECT_ROOT>/node_modules/react-native/\1'
module.name_mapper='^@?[./a-zA-Z0-9$_-]+\.\(bmp\|gif\|jpg\|jpeg\|png\|psd\|svg\|webp\|m4v\|mov\|mp4\|mpeg\|mpg\|webm\|aac\|aiff\|caf\|m4a\|mp3\|wav\|html\|pdf\)$' -> '<PROJECT_ROOT>/node_modules/react-native/Libraries/Image/RelativeImageStub'

suppress_type=$FlowIssue
suppress_type=$FlowFixMe
suppress_type=$FlowFixMeProps
suppress_type=$FlowFixMeState

[lints]
sketchy-null-number=warn
sketchy-null-mixed=warn
sketchy-number=warn
untyped-type-import=warn
nonstrict-import=warn
deprecated-type=warn
unsafe-getters-setters=warn
unnecessary-invariant=warn

[strict]
deprecated-type
nonstrict-import
sketchy-null
unclear-type
unsafe-getters-setters
untyped-import
untyped-type-import

[version]
^0.259.1
