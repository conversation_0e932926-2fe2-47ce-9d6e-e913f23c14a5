name: CI
on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup
        uses: ./.github/actions/setup

      - name: Lint files
        run: yarn lint

      - name: Typecheck files
        run: yarn typecheck

  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup
        uses: ./.github/actions/setup

      - name: Run unit tests
        run: yarn test --maxWorkers=2 --coverage

  build-library:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup
        uses: ./.github/actions/setup

      - name: Build package
        run: yarn prepack

  build-android:
    runs-on: macos-latest
    strategy:
      fail-fast: false
      matrix:
        configurations:
          [
            { react_native_version: '0.77.0', node_version: 18 },
            { react_native_version: '0.76.6', node_version: 18 },
            # { react_native_version: '0.75.4', node_version: 18 }
          ]
    env:
      TURBO_CACHE_DIR: .turbo/android
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup
        uses: ./.github/actions/setup_test_project
        with:
          REACT_NATIVE_VERSION: ${{ matrix.configurations.react_native_version }}
          NODE_VERSION: ${{ matrix.configurations.node_version }}
          IS_EXPO: ${{ matrix.configurations.is_expo }}

      - name: Cache turborepo for Android
        uses: actions/cache@v3
        with:
          path: ${{ env.TURBO_CACHE_DIR }}
          key: ${{ runner.os }}-${{ matrix.configurations.react_native_version }}-turborepo-android-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.configurations.react_native_version }}-turborepo-android-

      - name: Check turborepo cache for Android
        run: |
          TURBO_CACHE_STATUS=$(node -p "($(yarn --silent turbo run test:android --cache-dir="${{ env.TURBO_CACHE_DIR }}" --dry=json)).tasks.find(t => t.task === 'test:android').cache.status")

          if [[ $TURBO_CACHE_STATUS == "HIT" ]]; then
            echo "turbo_cache_hit=1" >> $GITHUB_ENV
          fi

      - name: Install JDK
        if: env.turbo_cache_hit != 1
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: '17'

      - name: Cache Gradle
        if: env.turbo_cache_hit != 1
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/wrapper
            ~/.gradle/caches
          key: ${{ runner.os }}-${{ matrix.configurations.react_native_version }}-gradle-${{ hashFiles('example/android/gradle/wrapper/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.configurations.react_native_version }}-gradle-

      - name: Build example for Android
        run: |
          yarn turbo run test:android --cache-dir="${{ env.TURBO_CACHE_DIR }}"

  build-ios:
    runs-on: macos-latest
    strategy:
      fail-fast: false
      matrix:
        configurations:
          [
            { react_native_version: '0.77.0', node_version: 18 },
            { react_native_version: '0.76.6', node_version: 18 },
            { react_native_version: '0.75.4', node_version: 18 }
          ]
    env:
      TURBO_CACHE_DIR: .turbo/ios
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup
        uses: ./.github/actions/setup_test_project
        with:
          REACT_NATIVE_VERSION: ${{ matrix.configurations.react_native_version }}
          NODE_VERSION: ${{ matrix.configurations.node_version }}
          IS_EXPO: ${{ matrix.configurations.is_expo }}

      - name: Cache turborepo for iOS
        uses: actions/cache@v3
        with:
          path: ${{ env.TURBO_CACHE_DIR }}
          key: ${{ runner.os }}-${{ matrix.configurations.react_native_version }}-turborepo-ios-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.configurations.react_native_version }}-turborepo-ios-

      - name: Check turborepo cache for iOS
        run: |
          TURBO_CACHE_STATUS=$(node -p "($(yarn --silent turbo run test:ios --cache-dir="${{ env.TURBO_CACHE_DIR }}" --dry=json)).tasks.find(t => t.task === 'test:ios').cache.status")

          if [[ $TURBO_CACHE_STATUS == "HIT" ]]; then
            echo "turbo_cache_hit=1" >> $GITHUB_ENV
          fi

      - name: Cache cocoapods
        if: env.turbo_cache_hit != 1
        id: cocoapods-cache
        uses: actions/cache@v3
        with:
          path: |
            **/ios/Pods
          key: ${{ runner.os }}-${{ matrix.configurations.react_native_version }}-cocoapods-${{ hashFiles('example/ios/Podfile.lock') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.configurations.react_native_version }}-cocoapods-

      - name: Install cocoapods
        if: env.turbo_cache_hit != 1
        run: |
          cd example/ios
          pod install --verbose
        env:
          NO_FLIPPER: 1

      - name: Build example for iOS
        if: env.turbo_cache_hit != 1
        run: |
          yarn turbo run test:ios --cache-dir="${{ env.TURBO_CACHE_DIR }}"

  prebuild-expo:
    runs-on: macos-latest
    strategy:
      fail-fast: false
      matrix:
        configurations: [{ react_native_version: 'expo', node_version: 18, is_expo: 'true' }]
    env:
      TURBO_CACHE_DIR: .turbo/android
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup
        uses: ./.github/actions/setup_test_project
        with:
          REACT_NATIVE_VERSION: ${{ matrix.configurations.react_native_version }}
          NODE_VERSION: ${{ matrix.configurations.node_version }}
          IS_EXPO: ${{ matrix.configurations.is_expo }}

      - name: Cache turborepo for Android
        uses: actions/cache@v3
        with:
          path: ${{ env.TURBO_CACHE_DIR }}
          key: ${{ runner.os }}-${{ matrix.configurations.react_native_version }}-turborepo-android-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.configurations.react_native_version }}-turborepo-android-

      - name: Check turborepo cache for Expo
        run: |
          TURBO_CACHE_STATUS=$(node -p "($(yarn --silent turbo run test:android --cache-dir="${{ env.TURBO_CACHE_DIR }}" --dry=json)).tasks.find(t => t.task === 'test:android').cache.status")

          if [[ $TURBO_CACHE_STATUS == "HIT" ]]; then
            echo "turbo_cache_hit=1" >> $GITHUB_ENV
          fi

      - name: Install JDK
        if: env.turbo_cache_hit != 1
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: '17'

      - name: Finalize Android SDK
        if: env.turbo_cache_hit != 1
        run: |
          /bin/bash -c "yes | $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager --licenses > /dev/null"

      - name: Cache Gradle
        if: env.turbo_cache_hit != 1
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/wrapper
            ~/.gradle/caches
          key: ${{ runner.os }}-${{ matrix.configurations.react_native_version }}-gradle-${{ hashFiles('example/android/gradle/wrapper/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.configurations.react_native_version }}-gradle-

      - name: Prebuild example for expo
        run: yarn turbo run test:expo --cache-dir="${{ env.TURBO_CACHE_DIR }}"
