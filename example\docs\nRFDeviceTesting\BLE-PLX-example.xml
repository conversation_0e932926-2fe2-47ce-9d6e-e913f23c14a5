<server-configuration name="BLE-PLX-example">
   <service uuid="00001847-0000-1000-8000-00805f9b34fb">
      <characteristic uuid="00002a2b-0000-1000-8000-00805f9b34fb" value-string="blx-ple">
         <descriptor configure="CCCD"/>
         <descriptor uuid="0000290e-0000-1000-8000-00805f9b34fb" value="0001">
            <permission name="READ"/>
            <permission name="WRITE"/>
         </descriptor>
         <permission name="READ"/>
         <property name="NOTIFY"/>
         <property name="INDICATE"/>
      </characteristic>
      <characteristic uuid="00002b90-0000-1000-8000-00805f9b34fb">
         <permission name="READ"/>
         <permission name="WRITE"/>
         <property name="READ"/>
         <property name="WRITE"/>
         <property name="WRITE_WITHOUT_RESPONSE"/>
      </characteristic>
   </service>
</server-configuration>