<!doctype html>
<html lang="en">
<head>
  <meta charset='utf-8'>
  <title>react-native-ble-plx 3.3.0 | Documentation</title>
  <meta name='description' content='React Native Bluetooth Low Energy library'>
  <meta name='viewport' content='width=device-width,initial-scale=1'>
  <link href='assets/bass.css' rel='stylesheet'>
  <link href='assets/style.css' rel='stylesheet'>
  <link href='assets/github.css' rel='stylesheet'>
  <link href='assets/split.css' rel='stylesheet'>
</head>
<body class='documentation m0'>
    <div class='flex'>
      <div id='split-left' class='overflow-auto fs0 height-viewport-100'>
        <div class='py1 px2'>
          <h3 class='mb0 no-anchor'>react-native-ble-plx</h3>
          <div class='mb1'><code>3.3.0</code></div>
          <input
            placeholder='Filter'
            id='filter-input'
            class='col12 block input'
            spellcheck='false'
            autocapitalize='off'
            autocorrect='off'
            type='text' />
          <div id='toc'>
            <ul class='list-reset h5 py1-ul'>
              
                
                <li><a
                  href='#getting-started'
                  class="h5 bold black caps">
                  Getting started
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#tutorials'
                  class="h5 bold black caps">
                  Tutorials
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#main-classes'
                  class="h5 bold black caps">
                  Main Classes
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#blemanager'
                  class=" toggle-sibling">
                  BleManager
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#blemanagerdestroy'
                        class='regular pre-open'>
                        #destroy
                      </a></li>
                      
                      <li><a
                        href='#blemanagersetloglevel'
                        class='regular pre-open'>
                        #setLogLevel
                      </a></li>
                      
                      <li><a
                        href='#blemanagerloglevel'
                        class='regular pre-open'>
                        #logLevel
                      </a></li>
                      
                      <li><a
                        href='#blemanagercanceltransaction'
                        class='regular pre-open'>
                        #cancelTransaction
                      </a></li>
                      
                      <li><a
                        href='#blemanagerenable'
                        class='regular pre-open'>
                        #enable
                      </a></li>
                      
                      <li><a
                        href='#blemanagerdisable'
                        class='regular pre-open'>
                        #disable
                      </a></li>
                      
                      <li><a
                        href='#blemanagerstate'
                        class='regular pre-open'>
                        #state
                      </a></li>
                      
                      <li><a
                        href='#blemanageronstatechange'
                        class='regular pre-open'>
                        #onStateChange
                      </a></li>
                      
                      <li><a
                        href='#blemanagerstartdevicescan'
                        class='regular pre-open'>
                        #startDeviceScan
                      </a></li>
                      
                      <li><a
                        href='#blemanagerstopdevicescan'
                        class='regular pre-open'>
                        #stopDeviceScan
                      </a></li>
                      
                      <li><a
                        href='#blemanagerrequestconnectionpriorityfordevice'
                        class='regular pre-open'>
                        #requestConnectionPriorityForDevice
                      </a></li>
                      
                      <li><a
                        href='#blemanagerreadrssifordevice'
                        class='regular pre-open'>
                        #readRSSIForDevice
                      </a></li>
                      
                      <li><a
                        href='#blemanagerrequestmtufordevice'
                        class='regular pre-open'>
                        #requestMTUForDevice
                      </a></li>
                      
                      <li><a
                        href='#blemanagerdevices'
                        class='regular pre-open'>
                        #devices
                      </a></li>
                      
                      <li><a
                        href='#blemanagerconnecteddevices'
                        class='regular pre-open'>
                        #connectedDevices
                      </a></li>
                      
                      <li><a
                        href='#blemanagerconnecttodevice'
                        class='regular pre-open'>
                        #connectToDevice
                      </a></li>
                      
                      <li><a
                        href='#blemanagercanceldeviceconnection'
                        class='regular pre-open'>
                        #cancelDeviceConnection
                      </a></li>
                      
                      <li><a
                        href='#blemanagerondevicedisconnected'
                        class='regular pre-open'>
                        #onDeviceDisconnected
                      </a></li>
                      
                      <li><a
                        href='#blemanagerisdeviceconnected'
                        class='regular pre-open'>
                        #isDeviceConnected
                      </a></li>
                      
                      <li><a
                        href='#blemanagerdiscoverallservicesandcharacteristicsfordevice'
                        class='regular pre-open'>
                        #discoverAllServicesAndCharacteristicsForDevice
                      </a></li>
                      
                      <li><a
                        href='#blemanagerservicesfordevice'
                        class='regular pre-open'>
                        #servicesForDevice
                      </a></li>
                      
                      <li><a
                        href='#blemanagercharacteristicsfordevice'
                        class='regular pre-open'>
                        #characteristicsForDevice
                      </a></li>
                      
                      <li><a
                        href='#blemanagerdescriptorsfordevice'
                        class='regular pre-open'>
                        #descriptorsForDevice
                      </a></li>
                      
                      <li><a
                        href='#blemanagerreadcharacteristicfordevice'
                        class='regular pre-open'>
                        #readCharacteristicForDevice
                      </a></li>
                      
                      <li><a
                        href='#blemanagerwritecharacteristicwithresponsefordevice'
                        class='regular pre-open'>
                        #writeCharacteristicWithResponseForDevice
                      </a></li>
                      
                      <li><a
                        href='#blemanagerwritecharacteristicwithoutresponsefordevice'
                        class='regular pre-open'>
                        #writeCharacteristicWithoutResponseForDevice
                      </a></li>
                      
                      <li><a
                        href='#blemanagermonitorcharacteristicfordevice'
                        class='regular pre-open'>
                        #monitorCharacteristicForDevice
                      </a></li>
                      
                      <li><a
                        href='#blemanagerreaddescriptorfordevice'
                        class='regular pre-open'>
                        #readDescriptorForDevice
                      </a></li>
                      
                      <li><a
                        href='#blemanagerwritedescriptorfordevice'
                        class='regular pre-open'>
                        #writeDescriptorForDevice
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#device'
                  class=" toggle-sibling">
                  Device
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#deviceid'
                        class='regular pre-open'>
                        #id
                      </a></li>
                      
                      <li><a
                        href='#devicename'
                        class='regular pre-open'>
                        #name
                      </a></li>
                      
                      <li><a
                        href='#devicerssi'
                        class='regular pre-open'>
                        #rssi
                      </a></li>
                      
                      <li><a
                        href='#devicemtu'
                        class='regular pre-open'>
                        #mtu
                      </a></li>
                      
                      <li><a
                        href='#devicemanufacturerdata'
                        class='regular pre-open'>
                        #manufacturerData
                      </a></li>
                      
                      <li><a
                        href='#devicerawscanrecord'
                        class='regular pre-open'>
                        #rawScanRecord
                      </a></li>
                      
                      <li><a
                        href='#deviceservicedata'
                        class='regular pre-open'>
                        #serviceData
                      </a></li>
                      
                      <li><a
                        href='#deviceserviceuuids'
                        class='regular pre-open'>
                        #serviceUUIDs
                      </a></li>
                      
                      <li><a
                        href='#devicelocalname'
                        class='regular pre-open'>
                        #localName
                      </a></li>
                      
                      <li><a
                        href='#devicetxpowerlevel'
                        class='regular pre-open'>
                        #txPowerLevel
                      </a></li>
                      
                      <li><a
                        href='#devicesolicitedserviceuuids'
                        class='regular pre-open'>
                        #solicitedServiceUUIDs
                      </a></li>
                      
                      <li><a
                        href='#deviceisconnectable'
                        class='regular pre-open'>
                        #isConnectable
                      </a></li>
                      
                      <li><a
                        href='#deviceoverflowserviceuuids'
                        class='regular pre-open'>
                        #overflowServiceUUIDs
                      </a></li>
                      
                      <li><a
                        href='#devicerequestconnectionpriority'
                        class='regular pre-open'>
                        #requestConnectionPriority
                      </a></li>
                      
                      <li><a
                        href='#devicereadrssi'
                        class='regular pre-open'>
                        #readRSSI
                      </a></li>
                      
                      <li><a
                        href='#devicerequestmtu'
                        class='regular pre-open'>
                        #requestMTU
                      </a></li>
                      
                      <li><a
                        href='#deviceconnect'
                        class='regular pre-open'>
                        #connect
                      </a></li>
                      
                      <li><a
                        href='#devicecancelconnection'
                        class='regular pre-open'>
                        #cancelConnection
                      </a></li>
                      
                      <li><a
                        href='#deviceisconnected'
                        class='regular pre-open'>
                        #isConnected
                      </a></li>
                      
                      <li><a
                        href='#deviceondisconnected'
                        class='regular pre-open'>
                        #onDisconnected
                      </a></li>
                      
                      <li><a
                        href='#devicediscoverallservicesandcharacteristics'
                        class='regular pre-open'>
                        #discoverAllServicesAndCharacteristics
                      </a></li>
                      
                      <li><a
                        href='#deviceservices'
                        class='regular pre-open'>
                        #services
                      </a></li>
                      
                      <li><a
                        href='#devicecharacteristicsforservice'
                        class='regular pre-open'>
                        #characteristicsForService
                      </a></li>
                      
                      <li><a
                        href='#devicedescriptorsforservice'
                        class='regular pre-open'>
                        #descriptorsForService
                      </a></li>
                      
                      <li><a
                        href='#devicereadcharacteristicforservice'
                        class='regular pre-open'>
                        #readCharacteristicForService
                      </a></li>
                      
                      <li><a
                        href='#devicewritecharacteristicwithresponseforservice'
                        class='regular pre-open'>
                        #writeCharacteristicWithResponseForService
                      </a></li>
                      
                      <li><a
                        href='#devicewritecharacteristicwithoutresponseforservice'
                        class='regular pre-open'>
                        #writeCharacteristicWithoutResponseForService
                      </a></li>
                      
                      <li><a
                        href='#devicemonitorcharacteristicforservice'
                        class='regular pre-open'>
                        #monitorCharacteristicForService
                      </a></li>
                      
                      <li><a
                        href='#devicereaddescriptorforservice'
                        class='regular pre-open'>
                        #readDescriptorForService
                      </a></li>
                      
                      <li><a
                        href='#devicewritedescriptorforservice'
                        class='regular pre-open'>
                        #writeDescriptorForService
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#service'
                  class=" toggle-sibling">
                  Service
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#serviceid'
                        class='regular pre-open'>
                        #id
                      </a></li>
                      
                      <li><a
                        href='#serviceuuid'
                        class='regular pre-open'>
                        #uuid
                      </a></li>
                      
                      <li><a
                        href='#servicedeviceid'
                        class='regular pre-open'>
                        #deviceID
                      </a></li>
                      
                      <li><a
                        href='#serviceisprimary'
                        class='regular pre-open'>
                        #isPrimary
                      </a></li>
                      
                      <li><a
                        href='#servicecharacteristics'
                        class='regular pre-open'>
                        #characteristics
                      </a></li>
                      
                      <li><a
                        href='#servicedescriptorsforcharacteristic'
                        class='regular pre-open'>
                        #descriptorsForCharacteristic
                      </a></li>
                      
                      <li><a
                        href='#servicereadcharacteristic'
                        class='regular pre-open'>
                        #readCharacteristic
                      </a></li>
                      
                      <li><a
                        href='#servicewritecharacteristicwithresponse'
                        class='regular pre-open'>
                        #writeCharacteristicWithResponse
                      </a></li>
                      
                      <li><a
                        href='#servicewritecharacteristicwithoutresponse'
                        class='regular pre-open'>
                        #writeCharacteristicWithoutResponse
                      </a></li>
                      
                      <li><a
                        href='#servicemonitorcharacteristic'
                        class='regular pre-open'>
                        #monitorCharacteristic
                      </a></li>
                      
                      <li><a
                        href='#servicereaddescriptorforcharacteristic'
                        class='regular pre-open'>
                        #readDescriptorForCharacteristic
                      </a></li>
                      
                      <li><a
                        href='#servicewritedescriptorforcharacteristic'
                        class='regular pre-open'>
                        #writeDescriptorForCharacteristic
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#characteristic'
                  class=" toggle-sibling">
                  Characteristic
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#characteristicid'
                        class='regular pre-open'>
                        #id
                      </a></li>
                      
                      <li><a
                        href='#characteristicuuid'
                        class='regular pre-open'>
                        #uuid
                      </a></li>
                      
                      <li><a
                        href='#characteristicserviceid'
                        class='regular pre-open'>
                        #serviceID
                      </a></li>
                      
                      <li><a
                        href='#characteristicserviceuuid'
                        class='regular pre-open'>
                        #serviceUUID
                      </a></li>
                      
                      <li><a
                        href='#characteristicdeviceid'
                        class='regular pre-open'>
                        #deviceID
                      </a></li>
                      
                      <li><a
                        href='#characteristicisreadable'
                        class='regular pre-open'>
                        #isReadable
                      </a></li>
                      
                      <li><a
                        href='#characteristiciswritablewithresponse'
                        class='regular pre-open'>
                        #isWritableWithResponse
                      </a></li>
                      
                      <li><a
                        href='#characteristiciswritablewithoutresponse'
                        class='regular pre-open'>
                        #isWritableWithoutResponse
                      </a></li>
                      
                      <li><a
                        href='#characteristicisnotifiable'
                        class='regular pre-open'>
                        #isNotifiable
                      </a></li>
                      
                      <li><a
                        href='#characteristicisnotifying'
                        class='regular pre-open'>
                        #isNotifying
                      </a></li>
                      
                      <li><a
                        href='#characteristicisindicatable'
                        class='regular pre-open'>
                        #isIndicatable
                      </a></li>
                      
                      <li><a
                        href='#characteristicvalue'
                        class='regular pre-open'>
                        #value
                      </a></li>
                      
                      <li><a
                        href='#characteristicdescriptors'
                        class='regular pre-open'>
                        #descriptors
                      </a></li>
                      
                      <li><a
                        href='#characteristicread'
                        class='regular pre-open'>
                        #read
                      </a></li>
                      
                      <li><a
                        href='#characteristicwritewithresponse'
                        class='regular pre-open'>
                        #writeWithResponse
                      </a></li>
                      
                      <li><a
                        href='#characteristicwritewithoutresponse'
                        class='regular pre-open'>
                        #writeWithoutResponse
                      </a></li>
                      
                      <li><a
                        href='#characteristicmonitor'
                        class='regular pre-open'>
                        #monitor
                      </a></li>
                      
                      <li><a
                        href='#characteristicreaddescriptor'
                        class='regular pre-open'>
                        #readDescriptor
                      </a></li>
                      
                      <li><a
                        href='#characteristicwritedescriptor'
                        class='regular pre-open'>
                        #writeDescriptor
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#descriptor'
                  class=" toggle-sibling">
                  Descriptor
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#descriptorid'
                        class='regular pre-open'>
                        #id
                      </a></li>
                      
                      <li><a
                        href='#descriptoruuid'
                        class='regular pre-open'>
                        #uuid
                      </a></li>
                      
                      <li><a
                        href='#descriptorcharacteristicid'
                        class='regular pre-open'>
                        #characteristicID
                      </a></li>
                      
                      <li><a
                        href='#descriptorcharacteristicuuid'
                        class='regular pre-open'>
                        #characteristicUUID
                      </a></li>
                      
                      <li><a
                        href='#descriptorserviceid'
                        class='regular pre-open'>
                        #serviceID
                      </a></li>
                      
                      <li><a
                        href='#descriptorserviceuuid'
                        class='regular pre-open'>
                        #serviceUUID
                      </a></li>
                      
                      <li><a
                        href='#descriptordeviceid'
                        class='regular pre-open'>
                        #deviceID
                      </a></li>
                      
                      <li><a
                        href='#descriptorvalue'
                        class='regular pre-open'>
                        #value
                      </a></li>
                      
                      <li><a
                        href='#descriptorread'
                        class='regular pre-open'>
                        #read
                      </a></li>
                      
                      <li><a
                        href='#descriptorwrite'
                        class='regular pre-open'>
                        #write
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#utils'
                  class="h5 bold black caps">
                  Utils
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#fulluuid'
                  class="">
                  fullUUID
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#ble-error'
                  class="h5 bold black caps">
                  BLE Error
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#bleerror'
                  class=" toggle-sibling">
                  BleError
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#bleerrorerrorcode'
                        class='regular pre-open'>
                        #errorCode
                      </a></li>
                      
                      <li><a
                        href='#bleerroratterrorcode'
                        class='regular pre-open'>
                        #attErrorCode
                      </a></li>
                      
                      <li><a
                        href='#bleerrorioserrorcode'
                        class='regular pre-open'>
                        #iosErrorCode
                      </a></li>
                      
                      <li><a
                        href='#bleerrorandroiderrorcode'
                        class='regular pre-open'>
                        #androidErrorCode
                      </a></li>
                      
                      <li><a
                        href='#bleerrorreason'
                        class='regular pre-open'>
                        #reason
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#bleerrorcode'
                  class=" toggle-sibling">
                  BleErrorCode
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  <ul class='list-reset py1-ul pl1'>
                    <li class='h5'><span>Static members</span></li>
                    
                      <li><a
                        href='#bleerrorcodeunknownerror'
                        class='regular pre-open'>
                        .UnknownError
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodebluetoothmanagerdestroyed'
                        class='regular pre-open'>
                        .BluetoothManagerDestroyed
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodeoperationcancelled'
                        class='regular pre-open'>
                        .OperationCancelled
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodeoperationtimedout'
                        class='regular pre-open'>
                        .OperationTimedOut
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodeoperationstartfailed'
                        class='regular pre-open'>
                        .OperationStartFailed
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodeinvalididentifiers'
                        class='regular pre-open'>
                        .InvalidIdentifiers
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodebluetoothunsupported'
                        class='regular pre-open'>
                        .BluetoothUnsupported
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodebluetoothunauthorized'
                        class='regular pre-open'>
                        .BluetoothUnauthorized
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodebluetoothpoweredoff'
                        class='regular pre-open'>
                        .BluetoothPoweredOff
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodebluetoothinunknownstate'
                        class='regular pre-open'>
                        .BluetoothInUnknownState
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodebluetoothresetting'
                        class='regular pre-open'>
                        .BluetoothResetting
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodebluetoothstatechangefailed'
                        class='regular pre-open'>
                        .BluetoothStateChangeFailed
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodedeviceconnectionfailed'
                        class='regular pre-open'>
                        .DeviceConnectionFailed
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodedevicedisconnected'
                        class='regular pre-open'>
                        .DeviceDisconnected
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodedevicerssireadfailed'
                        class='regular pre-open'>
                        .DeviceRSSIReadFailed
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodedevicealreadyconnected'
                        class='regular pre-open'>
                        .DeviceAlreadyConnected
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodedevicenotfound'
                        class='regular pre-open'>
                        .DeviceNotFound
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodedevicenotconnected'
                        class='regular pre-open'>
                        .DeviceNotConnected
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodedevicemtuchangefailed'
                        class='regular pre-open'>
                        .DeviceMTUChangeFailed
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodeservicesdiscoveryfailed'
                        class='regular pre-open'>
                        .ServicesDiscoveryFailed
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodeincludedservicesdiscoveryfailed'
                        class='regular pre-open'>
                        .IncludedServicesDiscoveryFailed
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodeservicenotfound'
                        class='regular pre-open'>
                        .ServiceNotFound
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodeservicesnotdiscovered'
                        class='regular pre-open'>
                        .ServicesNotDiscovered
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodecharacteristicsdiscoveryfailed'
                        class='regular pre-open'>
                        .CharacteristicsDiscoveryFailed
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodecharacteristicwritefailed'
                        class='regular pre-open'>
                        .CharacteristicWriteFailed
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodecharacteristicreadfailed'
                        class='regular pre-open'>
                        .CharacteristicReadFailed
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodecharacteristicnotifychangefailed'
                        class='regular pre-open'>
                        .CharacteristicNotifyChangeFailed
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodecharacteristicnotfound'
                        class='regular pre-open'>
                        .CharacteristicNotFound
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodecharacteristicsnotdiscovered'
                        class='regular pre-open'>
                        .CharacteristicsNotDiscovered
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodecharacteristicinvaliddataformat'
                        class='regular pre-open'>
                        .CharacteristicInvalidDataFormat
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodedescriptorsdiscoveryfailed'
                        class='regular pre-open'>
                        .DescriptorsDiscoveryFailed
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodedescriptorwritefailed'
                        class='regular pre-open'>
                        .DescriptorWriteFailed
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodedescriptorreadfailed'
                        class='regular pre-open'>
                        .DescriptorReadFailed
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodedescriptornotfound'
                        class='regular pre-open'>
                        .DescriptorNotFound
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodedescriptorsnotdiscovered'
                        class='regular pre-open'>
                        .DescriptorsNotDiscovered
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodedescriptorinvaliddataformat'
                        class='regular pre-open'>
                        .DescriptorInvalidDataFormat
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodedescriptorwritenotallowed'
                        class='regular pre-open'>
                        .DescriptorWriteNotAllowed
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodescanstartfailed'
                        class='regular pre-open'>
                        .ScanStartFailed
                      </a></li>
                    
                      <li><a
                        href='#bleerrorcodelocationservicesdisabled'
                        class='regular pre-open'>
                        .LocationServicesDisabled
                      </a></li>
                    
                    </ul>
                  
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#bleerrorcodemessage'
                  class="">
                  BleErrorCodeMessage
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#bleatterrorcode'
                  class=" toggle-sibling">
                  BleATTErrorCode
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  <ul class='list-reset py1-ul pl1'>
                    <li class='h5'><span>Static members</span></li>
                    
                      <li><a
                        href='#bleatterrorcodesuccess'
                        class='regular pre-open'>
                        .Success
                      </a></li>
                    
                      <li><a
                        href='#bleatterrorcodeinvalidhandle'
                        class='regular pre-open'>
                        .InvalidHandle
                      </a></li>
                    
                      <li><a
                        href='#bleatterrorcodereadnotpermitted'
                        class='regular pre-open'>
                        .ReadNotPermitted
                      </a></li>
                    
                      <li><a
                        href='#bleatterrorcodewritenotpermitted'
                        class='regular pre-open'>
                        .WriteNotPermitted
                      </a></li>
                    
                      <li><a
                        href='#bleatterrorcodeinvalidpdu'
                        class='regular pre-open'>
                        .InvalidPdu
                      </a></li>
                    
                      <li><a
                        href='#bleatterrorcodeinsufficientauthentication'
                        class='regular pre-open'>
                        .InsufficientAuthentication
                      </a></li>
                    
                      <li><a
                        href='#bleatterrorcoderequestnotsupported'
                        class='regular pre-open'>
                        .RequestNotSupported
                      </a></li>
                    
                      <li><a
                        href='#bleatterrorcodeinvalidoffset'
                        class='regular pre-open'>
                        .InvalidOffset
                      </a></li>
                    
                      <li><a
                        href='#bleatterrorcodeinsufficientauthorization'
                        class='regular pre-open'>
                        .InsufficientAuthorization
                      </a></li>
                    
                      <li><a
                        href='#bleatterrorcodepreparequeuefull'
                        class='regular pre-open'>
                        .PrepareQueueFull
                      </a></li>
                    
                      <li><a
                        href='#bleatterrorcodeattributenotfound'
                        class='regular pre-open'>
                        .AttributeNotFound
                      </a></li>
                    
                      <li><a
                        href='#bleatterrorcodeattributenotlong'
                        class='regular pre-open'>
                        .AttributeNotLong
                      </a></li>
                    
                      <li><a
                        href='#bleatterrorcodeinsufficientencryptionkeysize'
                        class='regular pre-open'>
                        .InsufficientEncryptionKeySize
                      </a></li>
                    
                      <li><a
                        href='#bleatterrorcodeinvalidattributevaluelength'
                        class='regular pre-open'>
                        .InvalidAttributeValueLength
                      </a></li>
                    
                      <li><a
                        href='#bleatterrorcodeunlikelyerror'
                        class='regular pre-open'>
                        .UnlikelyError
                      </a></li>
                    
                      <li><a
                        href='#bleatterrorcodeinsufficientencryption'
                        class='regular pre-open'>
                        .InsufficientEncryption
                      </a></li>
                    
                      <li><a
                        href='#bleatterrorcodeunsupportedgrouptype'
                        class='regular pre-open'>
                        .UnsupportedGroupType
                      </a></li>
                    
                      <li><a
                        href='#bleatterrorcodeinsufficientresources'
                        class='regular pre-open'>
                        .InsufficientResources
                      </a></li>
                    
                    </ul>
                  
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#bleandroiderrorcode'
                  class=" toggle-sibling">
                  BleAndroidErrorCode
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  <ul class='list-reset py1-ul pl1'>
                    <li class='h5'><span>Static members</span></li>
                    
                      <li><a
                        href='#bleandroiderrorcodenoresources'
                        class='regular pre-open'>
                        .NoResources
                      </a></li>
                    
                      <li><a
                        href='#bleandroiderrorcodeinternalerror'
                        class='regular pre-open'>
                        .InternalError
                      </a></li>
                    
                      <li><a
                        href='#bleandroiderrorcodewrongstate'
                        class='regular pre-open'>
                        .WrongState
                      </a></li>
                    
                      <li><a
                        href='#bleandroiderrorcodedbfull'
                        class='regular pre-open'>
                        .DbFull
                      </a></li>
                    
                      <li><a
                        href='#bleandroiderrorcodebusy'
                        class='regular pre-open'>
                        .Busy
                      </a></li>
                    
                      <li><a
                        href='#bleandroiderrorcodeerror'
                        class='regular pre-open'>
                        .Error
                      </a></li>
                    
                      <li><a
                        href='#bleandroiderrorcodecmdstarted'
                        class='regular pre-open'>
                        .CmdStarted
                      </a></li>
                    
                      <li><a
                        href='#bleandroiderrorcodeillegalparameter'
                        class='regular pre-open'>
                        .IllegalParameter
                      </a></li>
                    
                      <li><a
                        href='#bleandroiderrorcodepending'
                        class='regular pre-open'>
                        .Pending
                      </a></li>
                    
                      <li><a
                        href='#bleandroiderrorcodeauthfail'
                        class='regular pre-open'>
                        .AuthFail
                      </a></li>
                    
                      <li><a
                        href='#bleandroiderrorcodemore'
                        class='regular pre-open'>
                        .More
                      </a></li>
                    
                      <li><a
                        href='#bleandroiderrorcodeinvalidcfg'
                        class='regular pre-open'>
                        .InvalidCfg
                      </a></li>
                    
                      <li><a
                        href='#bleandroiderrorcodeservicestarted'
                        class='regular pre-open'>
                        .ServiceStarted
                      </a></li>
                    
                      <li><a
                        href='#bleandroiderrorcodeencrypednomitm'
                        class='regular pre-open'>
                        .EncrypedNoMitm
                      </a></li>
                    
                      <li><a
                        href='#bleandroiderrorcodenotencrypted'
                        class='regular pre-open'>
                        .NotEncrypted
                      </a></li>
                    
                      <li><a
                        href='#bleandroiderrorcodecongested'
                        class='regular pre-open'>
                        .Congested
                      </a></li>
                    
                    </ul>
                  
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#bleioserrorcode'
                  class=" toggle-sibling">
                  BleIOSErrorCode
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  <ul class='list-reset py1-ul pl1'>
                    <li class='h5'><span>Static members</span></li>
                    
                      <li><a
                        href='#bleioserrorcodeunknown'
                        class='regular pre-open'>
                        .Unknown
                      </a></li>
                    
                      <li><a
                        href='#bleioserrorcodeinvalidparameters'
                        class='regular pre-open'>
                        .InvalidParameters
                      </a></li>
                    
                      <li><a
                        href='#bleioserrorcodeinvalidhandle'
                        class='regular pre-open'>
                        .InvalidHandle
                      </a></li>
                    
                      <li><a
                        href='#bleioserrorcodenotconnected'
                        class='regular pre-open'>
                        .NotConnected
                      </a></li>
                    
                      <li><a
                        href='#bleioserrorcodeoutofspace'
                        class='regular pre-open'>
                        .OutOfSpace
                      </a></li>
                    
                      <li><a
                        href='#bleioserrorcodeoperationcancelled'
                        class='regular pre-open'>
                        .OperationCancelled
                      </a></li>
                    
                      <li><a
                        href='#bleioserrorcodeconnectiontimeout'
                        class='regular pre-open'>
                        .ConnectionTimeout
                      </a></li>
                    
                      <li><a
                        href='#bleioserrorcodeperipheraldisconnected'
                        class='regular pre-open'>
                        .PeripheralDisconnected
                      </a></li>
                    
                      <li><a
                        href='#bleioserrorcodeuuidnotallowed'
                        class='regular pre-open'>
                        .UuidNotAllowed
                      </a></li>
                    
                      <li><a
                        href='#bleioserrorcodealreadyadvertising'
                        class='regular pre-open'>
                        .AlreadyAdvertising
                      </a></li>
                    
                      <li><a
                        href='#bleioserrorcodeconnectionfailed'
                        class='regular pre-open'>
                        .ConnectionFailed
                      </a></li>
                    
                      <li><a
                        href='#bleioserrorcodeconnectionlimitreached'
                        class='regular pre-open'>
                        .ConnectionLimitReached
                      </a></li>
                    
                      <li><a
                        href='#bleioserrorcodeunknowndevice'
                        class='regular pre-open'>
                        .UnknownDevice
                      </a></li>
                    
                    </ul>
                  
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#flow-types'
                  class="h5 bold black caps">
                  Flow Types
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#loglevel'
                  class=" toggle-sibling">
                  LogLevel
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  <ul class='list-reset py1-ul pl1'>
                    <li class='h5'><span>Static members</span></li>
                    
                      <li><a
                        href='#loglevelnone'
                        class='regular pre-open'>
                        .None
                      </a></li>
                    
                      <li><a
                        href='#loglevelverbose'
                        class='regular pre-open'>
                        .Verbose
                      </a></li>
                    
                      <li><a
                        href='#logleveldebug'
                        class='regular pre-open'>
                        .Debug
                      </a></li>
                    
                      <li><a
                        href='#loglevelinfo'
                        class='regular pre-open'>
                        .Info
                      </a></li>
                    
                      <li><a
                        href='#loglevelwarning'
                        class='regular pre-open'>
                        .Warning
                      </a></li>
                    
                      <li><a
                        href='#loglevelerror'
                        class='regular pre-open'>
                        .Error
                      </a></li>
                    
                    </ul>
                  
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#connectionpriority'
                  class=" toggle-sibling">
                  ConnectionPriority
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  <ul class='list-reset py1-ul pl1'>
                    <li class='h5'><span>Static members</span></li>
                    
                      <li><a
                        href='#connectionprioritybalanced'
                        class='regular pre-open'>
                        .Balanced
                      </a></li>
                    
                      <li><a
                        href='#connectionpriorityhigh'
                        class='regular pre-open'>
                        .High
                      </a></li>
                    
                      <li><a
                        href='#connectionprioritylowpower'
                        class='regular pre-open'>
                        .LowPower
                      </a></li>
                    
                    </ul>
                  
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#state'
                  class=" toggle-sibling">
                  State
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  <ul class='list-reset py1-ul pl1'>
                    <li class='h5'><span>Static members</span></li>
                    
                      <li><a
                        href='#stateunknown'
                        class='regular pre-open'>
                        .Unknown
                      </a></li>
                    
                      <li><a
                        href='#stateresetting'
                        class='regular pre-open'>
                        .Resetting
                      </a></li>
                    
                      <li><a
                        href='#stateunsupported'
                        class='regular pre-open'>
                        .Unsupported
                      </a></li>
                    
                      <li><a
                        href='#stateunauthorized'
                        class='regular pre-open'>
                        .Unauthorized
                      </a></li>
                    
                      <li><a
                        href='#statepoweredoff'
                        class='regular pre-open'>
                        .PoweredOff
                      </a></li>
                    
                      <li><a
                        href='#statepoweredon'
                        class='regular pre-open'>
                        .PoweredOn
                      </a></li>
                    
                    </ul>
                  
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#bleerrorcodemessagemapping'
                  class="">
                  BleErrorCodeMessageMapping
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#blemanageroptions'
                  class=" toggle-sibling">
                  BleManagerOptions
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#blemanageroptionsrestorestateidentifier'
                        class='regular pre-open'>
                        #restoreStateIdentifier
                      </a></li>
                      
                      <li><a
                        href='#blemanageroptionsrestorestatefunction'
                        class='regular pre-open'>
                        #restoreStateFunction
                      </a></li>
                      
                      <li><a
                        href='#blemanageroptionserrorcodestomessagesmapping'
                        class='regular pre-open'>
                        #errorCodesToMessagesMapping
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#blerestoredstate'
                  class=" toggle-sibling">
                  BleRestoredState
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#blerestoredstateconnectedperipherals'
                        class='regular pre-open'>
                        #connectedPeripherals
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#scanoptions'
                  class=" toggle-sibling">
                  ScanOptions
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#scanoptionsallowduplicates'
                        class='regular pre-open'>
                        #allowDuplicates
                      </a></li>
                      
                      <li><a
                        href='#scanoptionsscanmode'
                        class='regular pre-open'>
                        #scanMode
                      </a></li>
                      
                      <li><a
                        href='#scanoptionscallbacktype'
                        class='regular pre-open'>
                        #callbackType
                      </a></li>
                      
                      <li><a
                        href='#scanoptionslegacyscan'
                        class='regular pre-open'>
                        #legacyScan
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#scancallbacktype'
                  class=" toggle-sibling">
                  ScanCallbackType
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  <ul class='list-reset py1-ul pl1'>
                    <li class='h5'><span>Static members</span></li>
                    
                      <li><a
                        href='#scancallbacktypeallmatches'
                        class='regular pre-open'>
                        .AllMatches
                      </a></li>
                    
                      <li><a
                        href='#scancallbacktypefirstmatch'
                        class='regular pre-open'>
                        .FirstMatch
                      </a></li>
                    
                      <li><a
                        href='#scancallbacktypematchlost'
                        class='regular pre-open'>
                        .MatchLost
                      </a></li>
                    
                    </ul>
                  
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#scanmode'
                  class=" toggle-sibling">
                  ScanMode
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  <ul class='list-reset py1-ul pl1'>
                    <li class='h5'><span>Static members</span></li>
                    
                      <li><a
                        href='#scanmodeopportunistic'
                        class='regular pre-open'>
                        .Opportunistic
                      </a></li>
                    
                      <li><a
                        href='#scanmodelowpower'
                        class='regular pre-open'>
                        .LowPower
                      </a></li>
                    
                      <li><a
                        href='#scanmodebalanced'
                        class='regular pre-open'>
                        .Balanced
                      </a></li>
                    
                      <li><a
                        href='#scanmodelowlatency'
                        class='regular pre-open'>
                        .LowLatency
                      </a></li>
                    
                    </ul>
                  
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#connectionoptions'
                  class=" toggle-sibling">
                  ConnectionOptions
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#connectionoptionsautoconnect'
                        class='regular pre-open'>
                        #autoConnect
                      </a></li>
                      
                      <li><a
                        href='#connectionoptionsrequestmtu'
                        class='regular pre-open'>
                        #requestMTU
                      </a></li>
                      
                      <li><a
                        href='#connectionoptionsrefreshgatt'
                        class='regular pre-open'>
                        #refreshGatt
                      </a></li>
                      
                      <li><a
                        href='#connectionoptionstimeout'
                        class='regular pre-open'>
                        #timeout
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#deviceid'
                  class="">
                  DeviceId
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#identifier'
                  class="">
                  Identifier
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#uuid'
                  class="">
                  UUID
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#transactionid'
                  class="">
                  TransactionId
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#subscription'
                  class="">
                  Subscription
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#base64'
                  class="">
                  Base64
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#refreshgattmoment'
                  class="">
                  RefreshGattMoment
                  
                </a>
                
                </li>
              
            </ul>
          </div>
          <div class='mt1 h6 quiet'>
            <a href='https://documentation.js.org/reading-documentation.html'>Need help reading this?</a>
          </div>
        </div>
      </div>
      <div id='split-right' class='relative overflow-auto height-viewport-100'>
        
          
            <div class='keyline-top-not py2'><section class='py2 clearfix'>

  <h2 id='getting-started' class='mt0'>
    Getting started
  </h2>

  
    <h1 align="center" >
  <a href="https://github.com/dotintent/react-native-ble-plx"><img style="max-height: 300px;" alt="react-native-ble-plx" src="logo.png" /></a>
</h1>
<p>This guide is an introduction to BLE stack and APIs exported by this library. For further information you can refer to</p>
<ul>
<li>tutorials and API reference available in this documentation,</li>
<li><a href="https://github.com/dotintent/react-native-ble-plx/wiki">GitHub wiki</a>,</li>
<li>example app available in the repository.</li>
</ul>
<h3>Install and prepare package</h3>
<p>In the case of Expo, you will need to prepare a plugin config, detailed information can be found here: <a href="https://github.com/dotintent/react-native-ble-plx?tab=readme-ov-file#expo-sdk-43">https://github.com/dotintent/react-native-ble-plx?tab=readme-ov-file#expo-sdk-43</a>
In the case of react native CLI you need to configure two environments:</p>
<ul>
<li><a href="https://github.com/dotintent/react-native-ble-plx?tab=readme-ov-file#ios-example-setup">iOS</a></li>
<li><a href="https://github.com/dotintent/react-native-ble-plx?tab=readme-ov-file#android-example-setup">Android</a></li>
</ul>
<h3>Creating BLE Manager</h3>
<p>First step is to create BleManager instance which is an entry point to all available APIs. Make sure to create it after application started its execution. We can keep it as a static reference by either creating our own abstraction (ex.1) or by simply creating a new instance (ex.2).</p>
<h4>Ex.1</h4>
<pre class='hljs'><span class="hljs-keyword">import</span> { BleManager } <span class="hljs-keyword">from</span> <span class="hljs-string">'react-native-ble-plx'</span>

<span class="hljs-comment">// create your own singleton class</span>
<span class="hljs-keyword">class</span> BLEServiceInstance {
  manager: BleManager

  <span class="hljs-keyword">constructor</span>(<span class="hljs-params"></span>) {
    <span class="hljs-keyword">this</span>.manager = <span class="hljs-keyword">new</span> BleManager()
  }
}

<span class="hljs-keyword">export</span> <span class="hljs-keyword">const</span> BLEService = <span class="hljs-keyword">new</span> BLEServiceInstance()</pre>
<h4>Ex.2</h4>
<pre class='hljs'><span class="hljs-keyword">import</span> { BleManager } <span class="hljs-keyword">from</span> <span class="hljs-string">'react-native-ble-plx'</span>

<span class="hljs-keyword">export</span> <span class="hljs-keyword">const</span> manager = <span class="hljs-keyword">new</span> BleManager()</pre>
<p>When you don't need any BLE functionality you can destroy created instance by calling <code>manager.destroy()</code> function. You can then recreate <code>BleManager</code> later on.</p>
<h3>Ask for permissions</h3>
<p>Check if you requested following permissions</p>
<ul>
<li>PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION</li>
<li>PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN (necessary for api 31+ )</li>
<li>PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT (necessary for api 31+ )</li>
</ul>
<p>eg.</p>
<pre class='hljs'>requestBluetoothPermission = <span class="hljs-keyword">async</span> () =&gt; {
  <span class="hljs-keyword">if</span> (Platform.OS === <span class="hljs-string">'ios'</span>) {
    <span class="hljs-keyword">return</span> <span class="hljs-literal">true</span>
  }
  <span class="hljs-keyword">if</span> (Platform.OS === <span class="hljs-string">'android'</span> &amp;&amp; PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION) {
    <span class="hljs-keyword">const</span> apiLevel = <span class="hljs-built_in">parseInt</span>(Platform.Version.toString(), <span class="hljs-number">10</span>)

    <span class="hljs-keyword">if</span> (apiLevel &lt; <span class="hljs-number">31</span>) {
      <span class="hljs-keyword">const</span> granted = <span class="hljs-keyword">await</span> PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION)
      <span class="hljs-keyword">return</span> granted === PermissionsAndroid.RESULTS.GRANTED
    }
    <span class="hljs-keyword">if</span> (PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN &amp;&amp; PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT) {
      <span class="hljs-keyword">const</span> result = <span class="hljs-keyword">await</span> PermissionsAndroid.requestMultiple([
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
      ])

      <span class="hljs-keyword">return</span> (
        result[<span class="hljs-string">'android.permission.BLUETOOTH_CONNECT'</span>] === PermissionsAndroid.RESULTS.GRANTED &amp;&amp;
        result[<span class="hljs-string">'android.permission.BLUETOOTH_SCAN'</span>] === PermissionsAndroid.RESULTS.GRANTED &amp;&amp;
        result[<span class="hljs-string">'android.permission.ACCESS_FINE_LOCATION'</span>] === PermissionsAndroid.RESULTS.GRANTED
      )
    }
  }

  <span class="hljs-keyword">this</span>.showErrorToast(<span class="hljs-string">'Permission have not been granted'</span>)

  <span class="hljs-keyword">return</span> <span class="hljs-literal">false</span>
}</pre>
<p>With <code>neverForLocation</code> flag active, you can remove <code>ACCESS_FINE_LOCATION</code> permissions ask e.g.:</p>
<pre class='hljs'><span class="hljs-keyword">const</span> result = <span class="hljs-keyword">await</span> PermissionsAndroid.requestMultiple([
  PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
  PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT
])

<span class="hljs-keyword">return</span> (
  result[<span class="hljs-string">'android.permission.BLUETOOTH_CONNECT'</span>] === PermissionsAndroid.RESULTS.GRANTED &amp;&amp;
  result[<span class="hljs-string">'android.permission.BLUETOOTH_SCAN'</span>] === PermissionsAndroid.RESULTS.GRANTED
)</pre>
<h3>Waiting for Powered On state</h3>
<p>When iOS application launches BLE stack is not immediately available and we need to check its status.
To detect current state and following state changes we can use <code>onStateChange()</code> function:</p>
<pre class='hljs'>React.useEffect(<span class="hljs-function"><span class="hljs-params">()</span> =&gt;</span> {
  <span class="hljs-keyword">const</span> subscription = manager.onStateChange(<span class="hljs-function"><span class="hljs-params">state</span> =&gt;</span> {
    <span class="hljs-keyword">if</span> (state === <span class="hljs-string">'PoweredOn'</span>) {
      scanAndConnect()
      subscription.remove()
    }
  }, <span class="hljs-literal">true</span>)
  <span class="hljs-keyword">return</span> <span class="hljs-function"><span class="hljs-params">()</span> =&gt;</span> subscription.remove()
}, [manager])</pre>
<h3>Scanning devices</h3>
<p>Devices needs to be scanned first to be able to connect to them. There is a simple function
which allows only one callback to be registered to handle detected devices:</p>
<pre class='hljs'><span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">scanAndConnect</span>(<span class="hljs-params"></span>) </span>{
  manager.startDeviceScan(<span class="hljs-literal">null</span>, <span class="hljs-literal">null</span>, (error, device) =&gt; {
    <span class="hljs-keyword">if</span> (error) {
      <span class="hljs-comment">// Handle error (scanning will be stopped automatically)</span>
      <span class="hljs-keyword">return</span>
    }

    <span class="hljs-comment">// Check if it is a device you are looking for based on advertisement data</span>
    <span class="hljs-comment">// or other criteria.</span>
    <span class="hljs-keyword">if</span> (device.name === <span class="hljs-string">'TI BLE Sensor Tag'</span> || device.name === <span class="hljs-string">'SensorTag'</span>) {
      <span class="hljs-comment">// Stop scanning as it's not necessary if you are scanning for one device.</span>
      manager.stopDeviceScan()

      <span class="hljs-comment">// Proceed with connection.</span>
    }
  })
}</pre>
<p>It is worth to note that scanning function may emit one device multiple times. However
when device is connected it won't broadcast and needs to be disconnected from central
to be scanned again. Only one scanning listener can be registered.</p>
<h4>Bluetooth 5 Advertisements in Android</h4>
<p>To see devices that use Bluetooth 5 Advertising Extension you have to set the <code>legacyScan</code> variable to <code>false</code> in <a href="#scanoptions">Scan options</a> when you are starting <a href="#blemanagerstartdevicescan">BleManager.startDeviceScan()</a>,</p>
<h3>Connecting and discovering services and characteristics</h3>
<p>Once device is scanned it is in disconnected state. We need to connect to it and discover
all services and characteristics it contains. Services may be understood
as containers grouping characteristics based on their meaning. Characteristic is a
container for a value which can be read, written and monitored based on available
capabilities. For example connection may look like this:</p>
<pre class='hljs'>device
  .connect()
  .then(<span class="hljs-function"><span class="hljs-params">device</span> =&gt;</span> {
    <span class="hljs-keyword">return</span> device.discoverAllServicesAndCharacteristics()
  })
  .then(<span class="hljs-function"><span class="hljs-params">device</span> =&gt;</span> {
    <span class="hljs-comment">// Do work on device with services and characteristics</span>
  })
  .catch(<span class="hljs-function"><span class="hljs-params">error</span> =&gt;</span> {
    <span class="hljs-comment">// Handle errors</span>
  })</pre>
<p>Discovery of services and characteristics is required to be executed once per connection*.
It can be a long process depending on number of characteristics and services available.</p>
<p>* Extremely rarely, when peripheral's service/characteristic set can change during a connection
an additional service discovery may be needed.</p>
<h3>Read, write and monitor values</h3>
<p>After successful discovery of services you can call</p>
<ul>
<li><a href="#blemanagerreadcharacteristicfordevice">BleManager.readCharacteristicForDevice()</a>,</li>
<li><a href="#blemanagerwritecharacteristicwithresponsefordevice">BleManager.writeCharacteristicWithResponseForDevice()</a>,</li>
<li><a href="#blemanagermonitorcharacteristicfordevice">BleManager.monitorCharacteristicForDevice()</a></li>
</ul>
<p>and other functions which are described in detail in documentation. You can also check our <em>example app</em> which is available in the repository.</p>

  
</section></div>
          
        
          
            <div class='keyline-top-not py2'><section class='py2 clearfix'>

  <h2 id='tutorials' class='mt0'>
    Tutorials
  </h2>

  
    <h3>Monitoring device connection</h3>
<p><code>onDeviceDisconnected</code> method allows you to monitor the disconnection of a device, more about the method can be found <a href="#here">BleManager.onDeviceDisconnected()</a>. Using it you can implement your own logic to handle the disconnection event. For example, you can try to reconnect to the device or show a notification to the user.</p>
<p>Note: connection will be monitored only when app is in foreground.</p>
<pre class='hljs'><span class="hljs-keyword">const</span> setupOnDeviceDisconnected = <span class="hljs-function">(<span class="hljs-params">deviceIdToMonitor: <span class="hljs-built_in">String</span></span>) =&gt;</span> {
  bleManagerInstance.onDeviceDisconnected(deviceIdToMonitor, disconnectedListener)
}

<span class="hljs-keyword">const</span> disconnectedListener = <span class="hljs-function">(<span class="hljs-params">error: BleError | <span class="hljs-literal">null</span>, device: Device | <span class="hljs-literal">null</span></span>) =&gt;</span> {
  <span class="hljs-keyword">if</span> (error) {
    <span class="hljs-built_in">console</span>.error(<span class="hljs-built_in">JSON</span>.stringify(error, <span class="hljs-literal">null</span>, <span class="hljs-number">4</span>))
  }
  <span class="hljs-keyword">if</span> (device) {
    <span class="hljs-built_in">console</span>.info(<span class="hljs-built_in">JSON</span>.stringify(device, <span class="hljs-literal">null</span>, <span class="hljs-number">4</span>))

    <span class="hljs-comment">// reconnect to the device</span>
    device.connect()
  }
}</pre>
<h3>Reading and writing to characteristics</h3>
<h4>Prepare the connection with your device</h4>
<p>The first thing you need to do is connect to your device. Once the connection is established you can only perform basic operations without the possibility of interacting with the services on the device. To be able to interact with the services on the device, so you need to call an additional command <code>device.discoverAllServicesAndCharacteristics()</code> because even though you know what service and characteristics are on the device, they all must be visible to the GATT client, which handles all operations.</p>
<pre class='hljs'>device
  .connect()
  .then(<span class="hljs-function"><span class="hljs-params">device</span> =&gt;</span> {
    <span class="hljs-keyword">return</span> device.discoverAllServicesAndCharacteristics()
  })
  .then(<span class="hljs-function"><span class="hljs-params">device</span> =&gt;</span> {
    <span class="hljs-comment">// A fully functional connection you can use, now you can read, write and monitor values</span>
  })
  .catch(<span class="hljs-function"><span class="hljs-params">error</span> =&gt;</span> {
    <span class="hljs-comment">// Handle errors</span>
  })</pre>
<h4>Reading from a characteristic</h4>
<p>To read a value from a characteristic, you need to call the <code>readCharacteristic</code> method on the device object. The method returns a promise that resolves to the characteristic value.</p>
<pre class='hljs'>device
  .readCharacteristicForService(serviceUUID, characteristicUUID)
  .then(<span class="hljs-function"><span class="hljs-params">characteristic</span> =&gt;</span> {
    <span class="hljs-built_in">console</span>.log(<span class="hljs-string">'Read characteristic value:'</span>, characteristic.value)
  })
  .catch(<span class="hljs-function"><span class="hljs-params">error</span> =&gt;</span> {
    <span class="hljs-built_in">console</span>.error(<span class="hljs-string">'Read characteristic error:'</span>, error)
  })</pre>
<h4>Writing to a characteristic</h4>
<p>To write a value to a characteristic, you need to call the <code>writeCharacteristicWithResponse</code> or <code>writeCharacteristicWithoutResponse</code> method on the device object. The method returns a promise that resolves when the write operation is completed.</p>
<pre class='hljs'>device
  .writeCharacteristicWithResponseForService(serviceUUID, characteristicUUID, value)
  .then(<span class="hljs-function"><span class="hljs-params">()</span> =&gt;</span> {
    <span class="hljs-built_in">console</span>.log(<span class="hljs-string">'Write characteristic success'</span>)
  })
  .catch(<span class="hljs-function"><span class="hljs-params">error</span> =&gt;</span> {
    <span class="hljs-built_in">console</span>.error(<span class="hljs-string">'Write characteristic error:'</span>, error)
  })</pre>
<h3>Connecting to a device that is already connected to the OS</h3>
<p>If you want to connect to a device that isn't discoverable because it is already connected to the system, you can use the <code>getConnectedDevices</code> method to get a list of connected devices. Then you can use the <code>connect</code> method on the device object to connect to the device, after making sure that the device is not already connected.</p>
<pre class='hljs'>bleManagerInstance
  .getConnectedDevices([serviceUUIDs])
  .then(<span class="hljs-function"><span class="hljs-params">devices</span> =&gt;</span> {
    <span class="hljs-keyword">const</span> device = devices.find(<span class="hljs-function"><span class="hljs-params">d</span> =&gt;</span> d.id === deviceIdWeWantToConnectTo)

    <span class="hljs-keyword">if</span> (device &amp;&amp; !device.isConnected) {
      device.connect()
    }
  })
  .catch(<span class="hljs-function"><span class="hljs-params">error</span> =&gt;</span> {
    <span class="hljs-built_in">console</span>.error(<span class="hljs-string">'Get connected devices error:'</span>, error)
  })</pre>
<h3>Collecting native logs</h3>
<p>If you encounter any issues with the library, you can enable native logs to get more information about what is happening under the hood. To enable native logs, you need to set the <code>logLevel</code> property on the BleManager instance to <code>LogLevel.Verbose</code>.</p>
<pre class='hljs'>bleManagerInstance.setLogLevel(LogLevel.Verbose)</pre>
<h4>Android</h4>
<p>To collect native logs on Android, you can open the Logcat in Android Studio and set filters to <code>package:mine (tag:BluetoothGatt | tag:ReactNativeJS | RxBle)</code>.</p>
<h4>iOS</h4>
<p>To collect native logs on iOS, you can open the Xcode console.</p>

  
</section></div>
          
        
          
            <div class='keyline-top-not py2'><section class='py2 clearfix'>

  <h2 id='main-classes' class='mt0'>
    Main Classes
  </h2>

  
    <p>Classes described below are main building blocks for your BLE support. They
are presented in order which aligns them with usage.</p>

  
</section></div>
          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='blemanager'>
      BleManager
    </h3>
    
    
  </div>
  

  <p>BleManager is an entry point for react-native-ble-plx library. It provides all means to discover and work with
<a href="#device">Device</a> instances. It should be initialized only once with <code>new</code> keyword and method
<a href="#blemanagerdestroy">destroy()</a> should be called on its instance when user wants to deallocate all resources.</p>
<p>In case you want to properly support Background Mode, you should provide <code>restoreStateIdentifier</code> and
<code>restoreStateFunction</code> in <a href="#blemanageroptions">BleManagerOptions</a>.</p>

    <div class='pre p1 fill-light mt0'>new BleManager(options: <a href="#blemanageroptions">BleManagerOptions</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>options</span> <code class='quiet'>(<a href="#blemanageroptions">BleManagerOptions</a>
            = <code>{}</code>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-keyword">const</span> manager = <span class="hljs-keyword">new</span> BleManager();
<span class="hljs-comment">// ... work with BLE manager ...</span>
manager.destroy();</pre>
    
  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='blemanagerdestroy'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>destroy()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Destroys <a href="#blemanager">BleManager</a> instance. A new instance needs to be created to continue working with
this library. All operations which were in progress completes with</p>

    <div class='pre p1 fill-light mt0'>destroy(): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;void></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;void></code>:
        Promise may return an error when the function cannot be called.

<a href="#bleerrorcodebluetoothmanagerdestroyed">BluetoothManagerDestroyed</a>
 error code.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagersetloglevel'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>setLogLevel(logLevel)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Sets new log level for native module's logging mechanism.</p>

    <div class='pre p1 fill-light mt0'>setLogLevel(logLevel: <a href="#loglevel">LogLevel</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#loglevel">LogLevel</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>logLevel</span> <code class='quiet'>(<a href="#loglevel">LogLevel</a>)</code>
	    New log level to be set.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#loglevel">LogLevel</a>></code>:
        Current log level.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerloglevel'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>logLevel()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get current log level for native module's logging mechanism.</p>

    <div class='pre p1 fill-light mt0'>logLevel(): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#loglevel">LogLevel</a>></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#loglevel">LogLevel</a>></code>:
        Current log level.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagercanceltransaction'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>cancelTransaction(transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Cancels pending transaction.</p>
<p>Few operations such as monitoring characteristic's value changes can be cancelled by a user. Basically every API
entry which accepts <code>transactionId</code> allows to call <code>cancelTransaction</code> function. When cancelled operation is a
promise or a callback which registers errors, <a href="#bleerror">BleError</a> with error code
<a href="#bleerrorcodeoperationcancelled">OperationCancelled</a> will be emitted in that case. Cancelling transaction
which doesn't exist is ignored.</p>

    <div class='pre p1 fill-light mt0'>cancelTransaction(transactionId: <a href="#transactionid">TransactionId</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;void></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>)</code>
	    Id of pending transactions.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;void></code>:
        

      
    
  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-keyword">const</span> transactionId = <span class="hljs-string">'monitor_battery'</span>;

<span class="hljs-comment">// Monitor battery notifications</span>
manager.monitorCharacteristicForDevice(
  device.id, <span class="hljs-string">'180F'</span>, <span class="hljs-string">'2A19'</span>,
  (error, characteristic) =&gt; {
  <span class="hljs-comment">// Handle battery level changes...</span>
}, transactionId);

<span class="hljs-comment">// Cancel after specified amount of time</span>
setTimeout(<span class="hljs-function"><span class="hljs-params">()</span> =&gt;</span> manager.cancelTransaction(transactionId), <span class="hljs-number">2000</span>);</pre>
    
  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerenable'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>enable(transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Enable Bluetooth. This function blocks until BLE is in PoweredOn state. [Android only]</p>

    <div class='pre p1 fill-light mt0'>enable(transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#blemanager">BleManager</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    Transaction handle used to cancel operation

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#blemanager">BleManager</a>></code>:
        Promise completes when state transition was successful.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerdisable'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>disable(transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Deprecated
Disable Bluetooth. This function blocks until BLE is in PoweredOff state. [Android only]</p>

    <div class='pre p1 fill-light mt0'>disable(transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#blemanager">BleManager</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    Transaction handle used to cancel operation

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#blemanager">BleManager</a>></code>:
        Promise completes when state transition was successful.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerstate'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>state()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Current, global <a href="#state">State</a> of a <a href="#blemanager">BleManager</a>. All APIs are working only when active state
is "PoweredOn".</p>

    <div class='pre p1 fill-light mt0'>state(): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#state">State</a>></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#state">State</a>></code>:
        Promise which emits current state of BleManager.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanageronstatechange'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>onStateChange(listener, emitCurrentState)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Notifies about <a href="#state">State</a> changes of a <a href="#blemanager">BleManager</a>.</p>

    <div class='pre p1 fill-light mt0'>onStateChange(listener: function (newState: <a href="#state">State</a>), emitCurrentState: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>): <a href="#subscription">Subscription</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>listener</span> <code class='quiet'>(function (newState: <a href="#state">State</a>))</code>
	    Callback which emits state changes of BLE Manager.
Look at 
<a href="#state">State</a>
 for possible values.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>emitCurrentState</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>
            = <code>false</code>)</code>
	    If true, current state will be emitted as well. Defaults to false.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#subscription">Subscription</a></code>:
        Subscription on which 
<code>remove()</code>
 function can be called to unsubscribe.

      
    
  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-keyword">const</span> subscription = <span class="hljs-keyword">this</span>.manager.onStateChange(<span class="hljs-function">(<span class="hljs-params">state</span>) =&gt;</span> {
     <span class="hljs-keyword">if</span> (state === <span class="hljs-string">'PoweredOn'</span>) {
         <span class="hljs-keyword">this</span>.scanAndConnect();
         subscription.remove();
     }
 }, <span class="hljs-literal">true</span>);</pre>
    
  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerstartdevicescan'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>startDeviceScan(UUIDs, options, listener)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Starts device scanning. When previous scan is in progress it will be stopped before executing this command.</p>

    <div class='pre p1 fill-light mt0'>startDeviceScan(UUIDs: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#uuid">UUID</a>>?, options: <a href="#scanoptions">ScanOptions</a>?, listener: function (error: <a href="#bleerror">BleError</a>?, scannedDevice: <a href="#device">Device</a>?)): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;void></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>UUIDs</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#uuid">UUID</a>>?)</code>
	    Array of strings containing 
<a href="#uuid">UUID</a>
s of 
<a href="#service">Service</a>
s which are registered in
scanned 
<a href="#device">Device</a>
. If 
<code>null</code>
 is passed, all available 
<a href="#device">Device</a>
s will be scanned.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>options</span> <code class='quiet'>(<a href="#scanoptions">ScanOptions</a>?)</code>
	    Optional configuration for scanning operation.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>listener</span> <code class='quiet'>(function (error: <a href="#bleerror">BleError</a>?, scannedDevice: <a href="#device">Device</a>?))</code>
	    Function which will be called for every scanned

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;void></code>:
        Promise may return an error when the function cannot be called.

<a href="#device">Device</a>
 (devices may be scanned multiple times). It's first argument is potential 
<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Error">Error</a>
 which is set
to non 
<code>null</code>
 value when scanning failed. You have to start scanning process again if that happens. Second argument
is a scanned 
<a href="#device">Device</a>
.

      
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;void></code>:
        the promise may be rejected if the operation is impossible to perform.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerstopdevicescan'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>stopDeviceScan()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Stops <a href="#device">Device</a> scan if in progress.</p>

    <div class='pre p1 fill-light mt0'>stopDeviceScan(): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;void></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;void></code>:
        the promise may be rejected if the operation is impossible to perform.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerrequestconnectionpriorityfordevice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>requestConnectionPriorityForDevice(deviceIdentifier, connectionPriority, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Request a connection parameter update. This functions may update connection parameters on Android API level 21 or
above.</p>

    <div class='pre p1 fill-light mt0'>requestConnectionPriorityForDevice(deviceIdentifier: <a href="#deviceid">DeviceId</a>, connectionPriority: <a href="#connectionpriority">ConnectionPriority</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifier</span> <code class='quiet'>(<a href="#deviceid">DeviceId</a>)</code>
	    Device identifier.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>connectionPriority</span> <code class='quiet'>(<a href="#connectionpriority">ConnectionPriority</a>)</code>
	    : Connection priority.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    Transaction handle used to cancel operation.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></code>:
        Connected device.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerreadrssifordevice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>readRSSIForDevice(deviceIdentifier, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Reads RSSI for connected device.</p>

    <div class='pre p1 fill-light mt0'>readRSSIForDevice(deviceIdentifier: <a href="#deviceid">DeviceId</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifier</span> <code class='quiet'>(<a href="#deviceid">DeviceId</a>)</code>
	    Device identifier.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    Transaction handle used to cancel operation

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></code>:
        Connected device with updated RSSI value.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerrequestmtufordevice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>requestMTUForDevice(deviceIdentifier, mtu, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Request new MTU value for this device. This function currently is not doing anything
on iOS platform as MTU exchange is done automatically. Since Android 14,
mtu management has been changed, more information can be found at the link:
<a href="https://developer.android.com/about/versions/14/behavior-changes-all#mtu-set-to-517">https://developer.android.com/about/versions/14/behavior-changes-all#mtu-set-to-517</a></p>

    <div class='pre p1 fill-light mt0'>requestMTUForDevice(deviceIdentifier: <a href="#deviceid">DeviceId</a>, mtu: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifier</span> <code class='quiet'>(<a href="#deviceid">DeviceId</a>)</code>
	    Device identifier.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>mtu</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    New MTU to negotiate.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    Transaction handle used to cancel operation

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></code>:
        Device with updated MTU size. Default value is 23 (517 since Android 14)..

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerdevices'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>devices(deviceIdentifiers)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Returns a list of known devices by their identifiers.</p>

    <div class='pre p1 fill-light mt0'>devices(deviceIdentifiers: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#deviceid">DeviceId</a>>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#device">Device</a>>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifiers</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#deviceid">DeviceId</a>>)</code>
	    List of device identifiers.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#device">Device</a>>></code>:
        List of known devices by their identifiers.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerconnecteddevices'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>connectedDevices(serviceUUIDs)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Returns a list of the peripherals (containing any of the specified services) currently connected to the system
which have discovered services. Returned devices <strong>may not be connected</strong> to your application. Make sure to check
if that's the case with function <a href="#blemanagerisdeviceconnected">isDeviceConnected</a>.</p>

    <div class='pre p1 fill-light mt0'>connectedDevices(serviceUUIDs: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#uuid">UUID</a>>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#device">Device</a>>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>serviceUUIDs</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#uuid">UUID</a>>)</code>
	    List of service UUIDs. Device must contain at least one of them to be listed.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#device">Device</a>>></code>:
        List of known devices with discovered services as stated in the parameter.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerconnecttodevice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>connectToDevice(deviceIdentifier, options)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Connects to <a href="#device">Device</a> with provided ID.</p>

    <div class='pre p1 fill-light mt0'>connectToDevice(deviceIdentifier: <a href="#deviceid">DeviceId</a>, options: <a href="#connectionoptions">ConnectionOptions</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifier</span> <code class='quiet'>(<a href="#deviceid">DeviceId</a>)</code>
	    <a href="#device">Device</a>
 identifier.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>options</span> <code class='quiet'>(<a href="#connectionoptions">ConnectionOptions</a>?)</code>
	    Platform specific options for connection establishment.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></code>:
        Connected 
<a href="#device">Device</a>
 object if successful.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagercanceldeviceconnection'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>cancelDeviceConnection(deviceIdentifier)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Disconnects from <a href="#device">Device</a> if it's connected or cancels pending connection.</p>

    <div class='pre p1 fill-light mt0'>cancelDeviceConnection(deviceIdentifier: <a href="#deviceid">DeviceId</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifier</span> <code class='quiet'>(<a href="#deviceid">DeviceId</a>)</code>
	    <a href="#device">Device</a>
 identifier to be closed.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></code>:
        Returns closed 
<a href="#device">Device</a>
 when operation is successful.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerondevicedisconnected'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>onDeviceDisconnected(deviceIdentifier, listener)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Monitors if <a href="#device">Device</a> was disconnected due to any errors or connection problems.</p>

    <div class='pre p1 fill-light mt0'>onDeviceDisconnected(deviceIdentifier: <a href="#deviceid">DeviceId</a>, listener: function (error: <a href="#bleerror">BleError</a>?, device: <a href="#device">Device</a>)): <a href="#subscription">Subscription</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifier</span> <code class='quiet'>(<a href="#deviceid">DeviceId</a>)</code>
	    <a href="#device">Device</a>
 identifier to be monitored.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>listener</span> <code class='quiet'>(function (error: <a href="#bleerror">BleError</a>?, device: <a href="#device">Device</a>))</code>
	    callback returning error as a reason of disconnection
if available and 
<a href="#device">Device</a>
 object. If an error is null, that means the connection was terminated by

<a href="#blemanagercanceldeviceconnection">bleManager.cancelDeviceConnection()</a>
 call.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#subscription">Subscription</a></code>:
        Subscription on which 
<code>remove()</code>
 function can be called to unsubscribe.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerisdeviceconnected'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isDeviceConnected(deviceIdentifier)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Check connection state of a <a href="#device">Device</a>.</p>

    <div class='pre p1 fill-light mt0'>isDeviceConnected(deviceIdentifier: <a href="#deviceid">DeviceId</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifier</span> <code class='quiet'>(<a href="#deviceid">DeviceId</a>)</code>
	    <a href="#device">Device</a>
 identifier.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>></code>:
        Promise which emits 
<code>true</code>
 if device is connected, and 
<code>false</code>
 otherwise.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerdiscoverallservicesandcharacteristicsfordevice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>discoverAllServicesAndCharacteristicsForDevice(deviceIdentifier, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Discovers all <a href="#service">Service</a>s,  <a href="#characteristic">Characteristic</a>s and <a href="#descriptor">Descriptor</a>s for <a href="#device">Device</a>.</p>

    <div class='pre p1 fill-light mt0'>discoverAllServicesAndCharacteristicsForDevice(deviceIdentifier: <a href="#deviceid">DeviceId</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifier</span> <code class='quiet'>(<a href="#deviceid">DeviceId</a>)</code>
	    <a href="#device">Device</a>
 identifier.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    Transaction handle used to cancel operation

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></code>:
        Promise which emits 
<a href="#device">Device</a>
 object if all available services and
characteristics have been discovered.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerservicesfordevice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>servicesForDevice(deviceIdentifier)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>List of discovered <a href="#service">Service</a>s for <a href="#device">Device</a>.</p>

    <div class='pre p1 fill-light mt0'>servicesForDevice(deviceIdentifier: <a href="#deviceid">DeviceId</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#service">Service</a>>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifier</span> <code class='quiet'>(<a href="#deviceid">DeviceId</a>)</code>
	    <a href="#device">Device</a>
 identifier.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#service">Service</a>>></code>:
        Promise which emits array of 
<a href="#service">Service</a>
 objects which are discovered for a

<a href="#device">Device</a>
.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagercharacteristicsfordevice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>characteristicsForDevice(deviceIdentifier, serviceUUID)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>List of discovered <a href="#characteristic">Characteristic</a>s for given <a href="#device">Device</a> and <a href="#service">Service</a>.</p>

    <div class='pre p1 fill-light mt0'>characteristicsForDevice(deviceIdentifier: <a href="#deviceid">DeviceId</a>, serviceUUID: <a href="#uuid">UUID</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#characteristic">Characteristic</a>>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifier</span> <code class='quiet'>(<a href="#deviceid">DeviceId</a>)</code>
	    <a href="#device">Device</a>
 identifier.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>serviceUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#service">Service</a>
 UUID.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#characteristic">Characteristic</a>>></code>:
        Promise which emits array of 
<a href="#characteristic">Characteristic</a>
 objects which are
discovered for a 
<a href="#device">Device</a>
 in specified 
<a href="#service">Service</a>
.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerdescriptorsfordevice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>descriptorsForDevice(deviceIdentifier, serviceUUID, characteristicUUID)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>List of discovered <a href="#descriptor">Descriptor</a>s for given <a href="#device">Device</a>, <a href="#service">Service</a> and <a href="#characteristic">Characteristic</a>.</p>

    <div class='pre p1 fill-light mt0'>descriptorsForDevice(deviceIdentifier: <a href="#deviceid">DeviceId</a>, serviceUUID: <a href="#uuid">UUID</a>, characteristicUUID: <a href="#uuid">UUID</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#descriptor">Descriptor</a>>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifier</span> <code class='quiet'>(<a href="#deviceid">DeviceId</a>)</code>
	    <a href="#device">Device</a>
 identifier.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>serviceUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#service">Service</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#descriptor">Descriptor</a>>></code>:
        Promise which emits array of 
<a href="#descriptor">Descriptor</a>
 objects which are
discovered for a 
<a href="#device">Device</a>
, 
<a href="#service">Service</a>
 in specified 
<a href="#characteristic">Characteristic</a>
.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerreadcharacteristicfordevice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>readCharacteristicForDevice(deviceIdentifier, serviceUUID, characteristicUUID, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Read <a href="#characteristic">Characteristic</a> value.</p>

    <div class='pre p1 fill-light mt0'>readCharacteristicForDevice(deviceIdentifier: <a href="#deviceid">DeviceId</a>, serviceUUID: <a href="#uuid">UUID</a>, characteristicUUID: <a href="#uuid">UUID</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifier</span> <code class='quiet'>(<a href="#deviceid">DeviceId</a>)</code>
	    <a href="#device">Device</a>
 identifier.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>serviceUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#service">Service</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></code>:
        Promise which emits first 
<a href="#characteristic">Characteristic</a>
 object matching specified
UUID paths. Latest value of 
<a href="#characteristic">Characteristic</a>
 will be stored inside returned object.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerwritecharacteristicwithresponsefordevice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>writeCharacteristicWithResponseForDevice(deviceIdentifier, serviceUUID, characteristicUUID, base64Value, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Write <a href="#characteristic">Characteristic</a> value with response.</p>

    <div class='pre p1 fill-light mt0'>writeCharacteristicWithResponseForDevice(deviceIdentifier: <a href="#deviceid">DeviceId</a>, serviceUUID: <a href="#uuid">UUID</a>, characteristicUUID: <a href="#uuid">UUID</a>, base64Value: <a href="#base64">Base64</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifier</span> <code class='quiet'>(<a href="#deviceid">DeviceId</a>)</code>
	    <a href="#device">Device</a>
 identifier.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>serviceUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#service">Service</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>base64Value</span> <code class='quiet'>(<a href="#base64">Base64</a>)</code>
	    Value in Base64 format.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></code>:
        Promise which emits first 
<a href="#characteristic">Characteristic</a>
 object matching specified
UUID paths. Latest value of characteristic may not be stored inside returned object.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerwritecharacteristicwithoutresponsefordevice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>writeCharacteristicWithoutResponseForDevice(deviceIdentifier, serviceUUID, characteristicUUID, base64Value, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Write <a href="#characteristic">Characteristic</a> value without response.</p>

    <div class='pre p1 fill-light mt0'>writeCharacteristicWithoutResponseForDevice(deviceIdentifier: <a href="#deviceid">DeviceId</a>, serviceUUID: <a href="#uuid">UUID</a>, characteristicUUID: <a href="#uuid">UUID</a>, base64Value: <a href="#base64">Base64</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifier</span> <code class='quiet'>(<a href="#deviceid">DeviceId</a>)</code>
	    <a href="#device">Device</a>
 identifier.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>serviceUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#service">Service</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>base64Value</span> <code class='quiet'>(<a href="#base64">Base64</a>)</code>
	    Value in Base64 format.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></code>:
        Promise which emits first 
<a href="#characteristic">Characteristic</a>
 object matching specified
UUID paths. Latest value of characteristic may not be stored inside returned object.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagermonitorcharacteristicfordevice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>monitorCharacteristicForDevice(deviceIdentifier, serviceUUID, characteristicUUID, listener, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Monitor value changes of a <a href="#characteristic">Characteristic</a>. If notifications are enabled they will be used
in favour of indications.</p>

    <div class='pre p1 fill-light mt0'>monitorCharacteristicForDevice(deviceIdentifier: <a href="#deviceid">DeviceId</a>, serviceUUID: <a href="#uuid">UUID</a>, characteristicUUID: <a href="#uuid">UUID</a>, listener: function (error: <a href="#bleerror">BleError</a>?, characteristic: <a href="#characteristic">Characteristic</a>?), transactionId: <a href="#transactionid">TransactionId</a>?): <a href="#subscription">Subscription</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifier</span> <code class='quiet'>(<a href="#deviceid">DeviceId</a>)</code>
	    <a href="#device">Device</a>
 identifier.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>serviceUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#service">Service</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>listener</span> <code class='quiet'>(function (error: <a href="#bleerror">BleError</a>?, characteristic: <a href="#characteristic">Characteristic</a>?))</code>
	    callback which emits

<a href="#characteristic">Characteristic</a>
 objects with modified value for each notification.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#subscription">Subscription</a></code>:
        Subscription on which 
<code>remove()</code>
 function can be called to unsubscribe.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerreaddescriptorfordevice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>readDescriptorForDevice(deviceIdentifier, serviceUUID, characteristicUUID, descriptorUUID, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Read <a href="#descriptor">Descriptor</a> value.</p>

    <div class='pre p1 fill-light mt0'>readDescriptorForDevice(deviceIdentifier: <a href="#deviceid">DeviceId</a>, serviceUUID: <a href="#uuid">UUID</a>, characteristicUUID: <a href="#uuid">UUID</a>, descriptorUUID: <a href="#uuid">UUID</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifier</span> <code class='quiet'>(<a href="#deviceid">DeviceId</a>)</code>
	    <a href="#device">Device</a>
 identifier.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>serviceUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#service">Service</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>descriptorUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#descriptor">Descriptor</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></code>:
        Promise which emits first 
<a href="#descriptor">Descriptor</a>
 object matching specified
UUID paths. Latest value of 
<a href="#descriptor">Descriptor</a>
 will be stored inside returned object.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanagerwritedescriptorfordevice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>writeDescriptorForDevice(deviceIdentifier, serviceUUID, characteristicUUID, descriptorUUID, valueBase64, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Write <a href="#descriptor">Descriptor</a> value.</p>

    <div class='pre p1 fill-light mt0'>writeDescriptorForDevice(deviceIdentifier: <a href="#deviceid">DeviceId</a>, serviceUUID: <a href="#uuid">UUID</a>, characteristicUUID: <a href="#uuid">UUID</a>, descriptorUUID: <a href="#uuid">UUID</a>, valueBase64: <a href="#base64">Base64</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>deviceIdentifier</span> <code class='quiet'>(<a href="#deviceid">DeviceId</a>)</code>
	    Connected device identifier

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>serviceUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    Service UUID

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    Characteristic UUID

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>descriptorUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    Descriptor UUID

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>valueBase64</span> <code class='quiet'>(<a href="#base64">Base64</a>)</code>
	    Value to be set coded in Base64

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    Transaction handle used to cancel operation

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></code>:
        Descriptor which saved passed value

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='device'>
      Device
    </h3>
    
    
  </div>
  

  <p>Device instance which can be retrieved only by calling
<a href="#blemanagerstartdevicescan">bleManager.startDeviceScan()</a>.</p>

    <div class='pre p1 fill-light mt0'>new Device(nativeDevice: NativeDevice, manager: <a href="#blemanager">BleManager</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>nativeDevice</span> <code class='quiet'>(NativeDevice)</code>
	    Native device properties

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>manager</span> <code class='quiet'>(<a href="#blemanager">BleManager</a>)</code>
	    <a href="#blemanager">BleManager</a>
 handle

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='deviceid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>id</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Device identifier: MAC address on Android and UUID on iOS.</p>

    <div class='pre p1 fill-light mt0'>id</div>
  
    <p>
      Type:
      <a href="#deviceid">DeviceId</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicename'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>name</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Device name if present</p>

    <div class='pre p1 fill-light mt0'>name</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicerssi'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>rssi</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Current Received Signal Strength Indication of device</p>

    <div class='pre p1 fill-light mt0'>rssi</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicemtu'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>mtu</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Current Maximum Transmission Unit for this device. When device is not connected
default value of 23 is used.</p>

    <div class='pre p1 fill-light mt0'>mtu</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicemanufacturerdata'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>manufacturerData</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Device's custom manufacturer data. Its format is defined by manufacturer.</p>

    <div class='pre p1 fill-light mt0'>manufacturerData</div>
  
    <p>
      Type:
      <a href="#base64">Base64</a>?
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicerawscanrecord'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>rawScanRecord</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Raw device scan data. When you have specific advertiser data,
you can implement your own processing.</p>

    <div class='pre p1 fill-light mt0'>rawScanRecord</div>
  
    <p>
      Type:
      <a href="#base64">Base64</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='deviceservicedata'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>serviceData</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Map of service UUIDs (as keys) with associated data (as values).</p>

    <div class='pre p1 fill-light mt0'>serviceData</div>
  
    <p>
      Type:
      {}?
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='deviceserviceuuids'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>serviceUUIDs</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>List of available services visible during scanning.</p>

    <div class='pre p1 fill-light mt0'>serviceUUIDs</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#uuid">UUID</a>>?
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicelocalname'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>localName</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>User friendly name of device.</p>

    <div class='pre p1 fill-light mt0'>localName</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicetxpowerlevel'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>txPowerLevel</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Transmission power level of device.</p>

    <div class='pre p1 fill-light mt0'>txPowerLevel</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicesolicitedserviceuuids'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>solicitedServiceUUIDs</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>List of solicited service UUIDs.</p>

    <div class='pre p1 fill-light mt0'>solicitedServiceUUIDs</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#uuid">UUID</a>>?
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='deviceisconnectable'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isConnectable</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Is device connectable. [iOS only]</p>

    <div class='pre p1 fill-light mt0'>isConnectable</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='deviceoverflowserviceuuids'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>overflowServiceUUIDs</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>List of overflow service UUIDs. [iOS only]</p>

    <div class='pre p1 fill-light mt0'>overflowServiceUUIDs</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#uuid">UUID</a>>?
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicerequestconnectionpriority'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>requestConnectionPriority(connectionPriority, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerrequestconnectionpriorityfordevice">bleManager.requestConnectionPriorityForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>requestConnectionPriority(connectionPriority: <a href="#connectionpriority">ConnectionPriority</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>connectionPriority</span> <code class='quiet'>(<a href="#connectionpriority">ConnectionPriority</a>)</code>
	    : Connection priority.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    Transaction handle used to cancel operation.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></code>:
        Connected device.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicereadrssi'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>readRSSI(transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerreadrssifordevice">bleManager.readRSSIForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>readRSSI(transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    Transaction handle used to cancel operation.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></code>:
        This device with updated RSSI value.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicerequestmtu'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>requestMTU(mtu, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerrequestmtufordevice">bleManager.requestMTUForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>requestMTU(mtu: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>mtu</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    Transaction handle used to cancel operation.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></code>:
        Device with updated MTU size. Default value is 23.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='deviceconnect'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>connect(options)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerconnecttodevice">bleManager.connectToDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>connect(options: <a href="#connectionoptions">ConnectionOptions</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>options</span> <code class='quiet'>(<a href="#connectionoptions">ConnectionOptions</a>?)</code>
	    Platform specific options for connection establishment. Not used currently.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></code>:
        Connected 
<a href="#device">Device</a>
 object if successful.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicecancelconnection'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>cancelConnection()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagercanceldeviceconnection">bleManager.cancelDeviceConnection()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>cancelConnection(): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></code>:
        Returns closed 
<a href="#device">Device</a>
 when operation is successful.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='deviceisconnected'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isConnected()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerisdeviceconnected">bleManager.isDeviceConnected()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>isConnected(): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>></code>:
        Promise which emits 
<code>true</code>
 if device is connected, and 
<code>false</code>
 otherwise.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='deviceondisconnected'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>onDisconnected(listener)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerondevicedisconnected">bleManager.onDeviceDisconnected()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>onDisconnected(listener: function (error: <a href="#bleerror">BleError</a>?, device: <a href="#device">Device</a>)): <a href="#subscription">Subscription</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>listener</span> <code class='quiet'>(function (error: <a href="#bleerror">BleError</a>?, device: <a href="#device">Device</a>))</code>
	    callback returning error as a reason of disconnection
if available and 
<a href="#device">Device</a>
 object. If an error is null, that means the connection was terminated by

<a href="#blemanagercanceldeviceconnection">bleManager.cancelDeviceConnection()</a>
 call.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#subscription">Subscription</a></code>:
        Subscription on which 
<code>remove()</code>
 function can be called to unsubscribe.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicediscoverallservicesandcharacteristics'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>discoverAllServicesAndCharacteristics(transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerdiscoverallservicesandcharacteristicsfordevice">bleManager.discoverAllServicesAndCharacteristicsForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>discoverAllServicesAndCharacteristics(transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    Transaction handle used to cancel operation

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#device">Device</a>></code>:
        Promise which emits 
<a href="#device">Device</a>
 object if all available services and
characteristics have been discovered.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='deviceservices'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>services()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerservicesfordevice">bleManager.servicesForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>services(): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#service">Service</a>>></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#service">Service</a>>></code>:
        Promise which emits array of 
<a href="#service">Service</a>
 objects which are discovered by this
device.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicecharacteristicsforservice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>characteristicsForService(serviceUUID)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagercharacteristicsfordevice">bleManager.characteristicsForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>characteristicsForService(serviceUUID: <a href="#uuid">UUID</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#characteristic">Characteristic</a>>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>serviceUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#service">Service</a>
 UUID.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#characteristic">Characteristic</a>>></code>:
        Promise which emits array of 
<a href="#characteristic">Characteristic</a>
 objects which are
discovered for a 
<a href="#device">Device</a>
 in specified 
<a href="#service">Service</a>
.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicedescriptorsforservice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>descriptorsForService(serviceUUID, characteristicUUID)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerdescriptorsfordevice">bleManager.descriptorsForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>descriptorsForService(serviceUUID: <a href="#uuid">UUID</a>, characteristicUUID: <a href="#uuid">UUID</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#descriptor">Descriptor</a>>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>serviceUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#service">Service</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#descriptor">Descriptor</a>>></code>:
        Promise which emits array of 
<a href="#descriptor">Descriptor</a>
 objects which are
discovered for this 
<a href="#characteristic">Characteristic</a>
.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicereadcharacteristicforservice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>readCharacteristicForService(serviceUUID, characteristicUUID, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerreadcharacteristicfordevice">bleManager.readCharacteristicForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>readCharacteristicForService(serviceUUID: <a href="#uuid">UUID</a>, characteristicUUID: <a href="#uuid">UUID</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>serviceUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#service">Service</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">bleManager.cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></code>:
        Promise which emits first 
<a href="#characteristic">Characteristic</a>
 object matching specified
UUID paths. Latest value of 
<a href="#characteristic">Characteristic</a>
 will be stored inside returned object.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicewritecharacteristicwithresponseforservice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>writeCharacteristicWithResponseForService(serviceUUID, characteristicUUID, valueBase64, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerwritecharacteristicwithresponsefordevice">bleManager.writeCharacteristicWithResponseForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>writeCharacteristicWithResponseForService(serviceUUID: <a href="#uuid">UUID</a>, characteristicUUID: <a href="#uuid">UUID</a>, valueBase64: <a href="#base64">Base64</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>serviceUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#service">Service</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>valueBase64</span> <code class='quiet'>(<a href="#base64">Base64</a>)</code>
	    Value in Base64 format.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">bleManager.cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></code>:
        Promise which emits first 
<a href="#characteristic">Characteristic</a>
 object matching specified
UUID paths. Latest value of characteristic may not be stored inside returned object.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicewritecharacteristicwithoutresponseforservice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>writeCharacteristicWithoutResponseForService(serviceUUID, characteristicUUID, valueBase64, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerwritecharacteristicwithoutresponsefordevice">bleManager.writeCharacteristicWithoutResponseForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>writeCharacteristicWithoutResponseForService(serviceUUID: <a href="#uuid">UUID</a>, characteristicUUID: <a href="#uuid">UUID</a>, valueBase64: <a href="#base64">Base64</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>serviceUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#service">Service</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>valueBase64</span> <code class='quiet'>(<a href="#base64">Base64</a>)</code>
	    Value in Base64 format.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">bleManager.cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></code>:
        Promise which emits first 
<a href="#characteristic">Characteristic</a>
 object matching specified
UUID paths. Latest value of characteristic may not be stored inside returned object.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicemonitorcharacteristicforservice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>monitorCharacteristicForService(serviceUUID, characteristicUUID, listener, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagermonitorcharacteristicfordevice">bleManager.monitorCharacteristicForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>monitorCharacteristicForService(serviceUUID: <a href="#uuid">UUID</a>, characteristicUUID: <a href="#uuid">UUID</a>, listener: function (error: <a href="#bleerror">BleError</a>?, characteristic: <a href="#characteristic">Characteristic</a>?), transactionId: <a href="#transactionid">TransactionId</a>?): <a href="#subscription">Subscription</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>serviceUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#service">Service</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>listener</span> <code class='quiet'>(function (error: <a href="#bleerror">BleError</a>?, characteristic: <a href="#characteristic">Characteristic</a>?))</code>
	    callback which emits

<a href="#characteristic">Characteristic</a>
 objects with modified value for each notification.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">bleManager.cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#subscription">Subscription</a></code>:
        Subscription on which 
<code>remove()</code>
 function can be called to unsubscribe.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicereaddescriptorforservice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>readDescriptorForService(serviceUUID, characteristicUUID, descriptorUUID, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerreaddescriptorfordevice">bleManager.readDescriptorForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>readDescriptorForService(serviceUUID: <a href="#uuid">UUID</a>, characteristicUUID: <a href="#uuid">UUID</a>, descriptorUUID: <a href="#uuid">UUID</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>serviceUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#service">Service</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>descriptorUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#descriptor">Descriptor</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></code>:
        Promise which emits first 
<a href="#descriptor">Descriptor</a>
 object matching specified
UUID paths. Latest value of 
<a href="#descriptor">Descriptor</a>
 will be stored inside returned object.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='devicewritedescriptorforservice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>writeDescriptorForService(serviceUUID, characteristicUUID, descriptorUUID, valueBase64, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerwritedescriptorfordevice">bleManager.writeDescriptorForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>writeDescriptorForService(serviceUUID: <a href="#uuid">UUID</a>, characteristicUUID: <a href="#uuid">UUID</a>, descriptorUUID: <a href="#uuid">UUID</a>, valueBase64: <a href="#base64">Base64</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>serviceUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#service">Service</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    Characteristic UUID

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>descriptorUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    Descriptor UUID

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>valueBase64</span> <code class='quiet'>(<a href="#base64">Base64</a>)</code>
	    Value to be set coded in Base64

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    Transaction handle used to cancel operation

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></code>:
        Descriptor which saved passed value.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='service'>
      Service
    </h3>
    
    
  </div>
  

  <p>Service object.</p>

    <div class='pre p1 fill-light mt0'>new Service(nativeService: NativeService, manager: <a href="#blemanager">BleManager</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>nativeService</span> <code class='quiet'>(NativeService)</code>
	    NativeService properties to be copied.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>manager</span> <code class='quiet'>(<a href="#blemanager">BleManager</a>)</code>
	    Current BleManager instance.

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='serviceid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>id</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Service unique identifier</p>

    <div class='pre p1 fill-light mt0'>id</div>
  
    <p>
      Type:
      <a href="#identifier">Identifier</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='serviceuuid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>uuid</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Service UUID</p>

    <div class='pre p1 fill-light mt0'>uuid</div>
  
    <p>
      Type:
      <a href="#uuid">UUID</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='servicedeviceid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>deviceID</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Device's ID to which service belongs</p>

    <div class='pre p1 fill-light mt0'>deviceID</div>
  
    <p>
      Type:
      <a href="#deviceid">DeviceId</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='serviceisprimary'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isPrimary</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Value indicating whether the type of service is primary or secondary.</p>

    <div class='pre p1 fill-light mt0'>isPrimary</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='servicecharacteristics'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>characteristics()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagercharacteristicsfordevice">bleManager.characteristicsForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>characteristics(): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#characteristic">Characteristic</a>>></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#characteristic">Characteristic</a>>></code>:
        Promise which emits array of 
<a href="#characteristic">Characteristic</a>
 objects which are
discovered for this service.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='servicedescriptorsforcharacteristic'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>descriptorsForCharacteristic(characteristicUUID)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerdescriptorsfordevice">bleManager.descriptorsForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>descriptorsForCharacteristic(characteristicUUID: <a href="#uuid">UUID</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#descriptor">Descriptor</a>>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#descriptor">Descriptor</a>>></code>:
        Promise which emits array of 
<a href="#descriptor">Descriptor</a>
 objects which are
discovered for this 
<a href="#service">Service</a>
 in specified 
<a href="#characteristic">Characteristic</a>
.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='servicereadcharacteristic'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>readCharacteristic(characteristicUUID, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerreadcharacteristicfordevice">bleManager.readCharacteristicForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>readCharacteristic(characteristicUUID: <a href="#uuid">UUID</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">bleManager.cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></code>:
        Promise which emits first 
<a href="#characteristic">Characteristic</a>
 object matching specified
UUID path. Latest value of 
<a href="#characteristic">Characteristic</a>
 will be stored inside returned object.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='servicewritecharacteristicwithresponse'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>writeCharacteristicWithResponse(characteristicUUID, valueBase64, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerwritecharacteristicwithresponsefordevice">bleManager.writeCharacteristicWithResponseForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>writeCharacteristicWithResponse(characteristicUUID: <a href="#uuid">UUID</a>, valueBase64: <a href="#base64">Base64</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>valueBase64</span> <code class='quiet'>(<a href="#base64">Base64</a>)</code>
	    Value in Base64 format.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">bleManager.cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></code>:
        Promise which emits first 
<a href="#characteristic">Characteristic</a>
 object matching specified
UUID path. Latest value of characteristic may not be stored inside returned object.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='servicewritecharacteristicwithoutresponse'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>writeCharacteristicWithoutResponse(characteristicUUID, valueBase64, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerwritecharacteristicwithoutresponsefordevice">bleManager.writeCharacteristicWithoutResponseForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>writeCharacteristicWithoutResponse(characteristicUUID: <a href="#uuid">UUID</a>, valueBase64: <a href="#base64">Base64</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>valueBase64</span> <code class='quiet'>(<a href="#base64">Base64</a>)</code>
	    Value in Base64 format.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">bleManager.cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></code>:
        Promise which emits first 
<a href="#characteristic">Characteristic</a>
 object matching specified
UUID path. Latest value of characteristic may not be stored inside returned object.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='servicemonitorcharacteristic'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>monitorCharacteristic(characteristicUUID, listener, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagermonitorcharacteristicfordevice">bleManager.monitorCharacteristicForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>monitorCharacteristic(characteristicUUID: <a href="#uuid">UUID</a>, listener: function (error: <a href="#bleerror">BleError</a>?, characteristic: <a href="#characteristic">Characteristic</a>?), transactionId: <a href="#transactionid">TransactionId</a>?): <a href="#subscription">Subscription</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>listener</span> <code class='quiet'>(function (error: <a href="#bleerror">BleError</a>?, characteristic: <a href="#characteristic">Characteristic</a>?))</code>
	    callback which emits

<a href="#characteristic">Characteristic</a>
 objects with modified value for each notification.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">bleManager.cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#subscription">Subscription</a></code>:
        Subscription on which 
<code>remove()</code>
 function can be called to unsubscribe.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='servicereaddescriptorforcharacteristic'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>readDescriptorForCharacteristic(characteristicUUID, descriptorUUID, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerreaddescriptorfordevice">bleManager.readDescriptorForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>readDescriptorForCharacteristic(characteristicUUID: <a href="#uuid">UUID</a>, descriptorUUID: <a href="#uuid">UUID</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#characteristic">Characteristic</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>descriptorUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#descriptor">Descriptor</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></code>:
        Promise which emits first 
<a href="#descriptor">Descriptor</a>
 object matching specified
UUID paths. Latest value of 
<a href="#descriptor">Descriptor</a>
 will be stored inside returned object.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='servicewritedescriptorforcharacteristic'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>writeDescriptorForCharacteristic(characteristicUUID, descriptorUUID, valueBase64, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerwritedescriptorfordevice">bleManager.writeDescriptorForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>writeDescriptorForCharacteristic(characteristicUUID: <a href="#uuid">UUID</a>, descriptorUUID: <a href="#uuid">UUID</a>, valueBase64: <a href="#base64">Base64</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>characteristicUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    Characteristic UUID

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>descriptorUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    Descriptor UUID

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>valueBase64</span> <code class='quiet'>(<a href="#base64">Base64</a>)</code>
	    Value to be set coded in Base64

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    Transaction handle used to cancel operation

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></code>:
        Descriptor which saved passed value.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='characteristic'>
      Characteristic
    </h3>
    
    
  </div>
  

  <p>Characteristic object.</p>

    <div class='pre p1 fill-light mt0'>new Characteristic(nativeCharacteristic: NativeCharacteristic, manager: <a href="#blemanager">BleManager</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>nativeCharacteristic</span> <code class='quiet'>(NativeCharacteristic)</code>
	    NativeCharacteristic

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>manager</span> <code class='quiet'>(<a href="#blemanager">BleManager</a>)</code>
	    BleManager

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='characteristicid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>id</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Characteristic unique identifier</p>

    <div class='pre p1 fill-light mt0'>id</div>
  
    <p>
      Type:
      <a href="#identifier">Identifier</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristicuuid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>uuid</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Characteristic UUID</p>

    <div class='pre p1 fill-light mt0'>uuid</div>
  
    <p>
      Type:
      <a href="#uuid">UUID</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristicserviceid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>serviceID</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Service's ID to which characteristic belongs</p>

    <div class='pre p1 fill-light mt0'>serviceID</div>
  
    <p>
      Type:
      <a href="#identifier">Identifier</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristicserviceuuid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>serviceUUID</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Service's UUID to which characteristic belongs</p>

    <div class='pre p1 fill-light mt0'>serviceUUID</div>
  
    <p>
      Type:
      <a href="#uuid">UUID</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristicdeviceid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>deviceID</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Device's ID to which characteristic belongs</p>

    <div class='pre p1 fill-light mt0'>deviceID</div>
  
    <p>
      Type:
      <a href="#deviceid">DeviceId</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristicisreadable'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isReadable</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>True if characteristic can be read</p>

    <div class='pre p1 fill-light mt0'>isReadable</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristiciswritablewithresponse'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isWritableWithResponse</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>True if characteristic can be written with response</p>

    <div class='pre p1 fill-light mt0'>isWritableWithResponse</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristiciswritablewithoutresponse'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isWritableWithoutResponse</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>True if characteristic can be written without response</p>

    <div class='pre p1 fill-light mt0'>isWritableWithoutResponse</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristicisnotifiable'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isNotifiable</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>True if characteristic can monitor value changes.</p>

    <div class='pre p1 fill-light mt0'>isNotifiable</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristicisnotifying'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isNotifying</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>True if characteristic is monitoring value changes without ACK.</p>

    <div class='pre p1 fill-light mt0'>isNotifying</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristicisindicatable'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isIndicatable</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>True if characteristic is monitoring value changes with ACK.</p>

    <div class='pre p1 fill-light mt0'>isIndicatable</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristicvalue'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>value</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Characteristic value if present</p>

    <div class='pre p1 fill-light mt0'>value</div>
  
    <p>
      Type:
      <a href="#base64">Base64</a>?
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristicdescriptors'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>descriptors()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerdescriptorsfordevice">bleManager.descriptorsForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>descriptors(): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#descriptor">Descriptor</a>>></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#descriptor">Descriptor</a>>></code>:
        Promise which emits array of 
<a href="#descriptor">Descriptor</a>
 objects which are
discovered for this 
<a href="#characteristic">Characteristic</a>
.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristicread'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>read(transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerreadcharacteristicfordevice">bleManager.readCharacteristicForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>read(transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">bleManager.cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></code>:
        Promise which emits this 
<a href="#characteristic">Characteristic</a>
. Latest value will be stored
inside returned object.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristicwritewithresponse'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>writeWithResponse(valueBase64, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerwritecharacteristicwithresponsefordevice">bleManager.writeCharacteristicWithResponseForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>writeWithResponse(valueBase64: <a href="#base64">Base64</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>valueBase64</span> <code class='quiet'>(<a href="#base64">Base64</a>)</code>
	    Value in Base64 format.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">bleManager.cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></code>:
        Promise which emits this 
<a href="#characteristic">Characteristic</a>
. Latest value may
not be stored inside returned object.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristicwritewithoutresponse'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>writeWithoutResponse(valueBase64, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerwritecharacteristicwithoutresponsefordevice">bleManager.writeCharacteristicWithoutResponseForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>writeWithoutResponse(valueBase64: <a href="#base64">Base64</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>valueBase64</span> <code class='quiet'>(<a href="#base64">Base64</a>)</code>
	    Value in Base64 format.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">bleManager.cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#characteristic">Characteristic</a>></code>:
        Promise which emits this 
<a href="#characteristic">Characteristic</a>
. Latest value may
not be stored inside returned object.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristicmonitor'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>monitor(listener, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagermonitorcharacteristicfordevice">bleManager.monitorCharacteristicForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>monitor(listener: function (error: <a href="#bleerror">BleError</a>?, characteristic: <a href="#characteristic">Characteristic</a>?), transactionId: <a href="#transactionid">TransactionId</a>?): <a href="#subscription">Subscription</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>listener</span> <code class='quiet'>(function (error: <a href="#bleerror">BleError</a>?, characteristic: <a href="#characteristic">Characteristic</a>?))</code>
	    callback which emits
this 
<a href="#characteristic">Characteristic</a>
 with modified value for each notification.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">bleManager.cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#subscription">Subscription</a></code>:
        Subscription on which 
<code>remove()</code>
 function can be called to unsubscribe.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristicreaddescriptor'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>readDescriptor(descriptorUUID, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerreaddescriptorfordevice">bleManager.readDescriptorForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>readDescriptor(descriptorUUID: <a href="#uuid">UUID</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>descriptorUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    <a href="#descriptor">Descriptor</a>
 UUID.

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></code>:
        Promise which emits first 
<a href="#descriptor">Descriptor</a>
 object matching specified
UUID paths. Latest value of 
<a href="#descriptor">Descriptor</a>
 will be stored inside returned object.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='characteristicwritedescriptor'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>writeDescriptor(descriptorUUID, valueBase64, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerwritedescriptorfordevice">bleManager.writeDescriptorForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>writeDescriptor(descriptorUUID: <a href="#uuid">UUID</a>, valueBase64: <a href="#base64">Base64</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>descriptorUUID</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    Descriptor UUID

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>valueBase64</span> <code class='quiet'>(<a href="#base64">Base64</a>)</code>
	    Value to be set coded in Base64

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    Transaction handle used to cancel operation

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></code>:
        Descriptor which saved passed value.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='descriptor'>
      Descriptor
    </h3>
    
    
  </div>
  

  <p>Descriptor object.</p>

    <div class='pre p1 fill-light mt0'>new Descriptor(nativeDescriptor: NativeDescriptor, manager: <a href="#blemanager">BleManager</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>nativeDescriptor</span> <code class='quiet'>(NativeDescriptor)</code>
	    NativeDescriptor

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>manager</span> <code class='quiet'>(<a href="#blemanager">BleManager</a>)</code>
	    BleManager

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='descriptorid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>id</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Descriptor unique identifier</p>

    <div class='pre p1 fill-light mt0'>id</div>
  
    <p>
      Type:
      <a href="#identifier">Identifier</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='descriptoruuid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>uuid</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Descriptor UUID</p>

    <div class='pre p1 fill-light mt0'>uuid</div>
  
    <p>
      Type:
      <a href="#uuid">UUID</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='descriptorcharacteristicid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>characteristicID</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Characteristic's ID to which descriptor belongs</p>

    <div class='pre p1 fill-light mt0'>characteristicID</div>
  
    <p>
      Type:
      <a href="#identifier">Identifier</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='descriptorcharacteristicuuid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>characteristicUUID</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Characteristic's UUID to which descriptor belongs</p>

    <div class='pre p1 fill-light mt0'>characteristicUUID</div>
  
    <p>
      Type:
      <a href="#uuid">UUID</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='descriptorserviceid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>serviceID</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Service's ID to which descriptor belongs</p>

    <div class='pre p1 fill-light mt0'>serviceID</div>
  
    <p>
      Type:
      <a href="#identifier">Identifier</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='descriptorserviceuuid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>serviceUUID</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Service's UUID to which descriptor belongs</p>

    <div class='pre p1 fill-light mt0'>serviceUUID</div>
  
    <p>
      Type:
      <a href="#uuid">UUID</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='descriptordeviceid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>deviceID</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Device's ID to which descriptor belongs</p>

    <div class='pre p1 fill-light mt0'>deviceID</div>
  
    <p>
      Type:
      <a href="#deviceid">DeviceId</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='descriptorvalue'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>value</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Descriptor value if present</p>

    <div class='pre p1 fill-light mt0'>value</div>
  
    <p>
      Type:
      <a href="#base64">Base64</a>?
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='descriptorread'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>read(transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerreaddescriptorfordevice">bleManager.readDescriptorForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>read(transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    optional 
<code>transactionId</code>
 which can be used in

<a href="#blemanagercanceltransaction">cancelTransaction()</a>
 function.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></code>:
        Promise which emits first 
<a href="#descriptor">Descriptor</a>
 object matching specified
UUID paths. Latest value of 
<a href="#descriptor">Descriptor</a>
 will be stored inside returned object.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='descriptorwrite'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>write(valueBase64, transactionId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p><a href="#blemanagerwritedescriptorfordevice">bleManager.writeDescriptorForDevice()</a> with partially filled arguments.</p>

    <div class='pre p1 fill-light mt0'>write(valueBase64: <a href="#base64">Base64</a>, transactionId: <a href="#transactionid">TransactionId</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>valueBase64</span> <code class='quiet'>(<a href="#base64">Base64</a>)</code>
	    Value to be set coded in Base64

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>transactionId</span> <code class='quiet'>(<a href="#transactionid">TransactionId</a>?)</code>
	    Transaction handle used to cancel operation

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="#descriptor">Descriptor</a>></code>:
        Descriptor which saved passed value.

      
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  
</section>

          
        
          
            <div class='keyline-top-not py2'><section class='py2 clearfix'>

  <h2 id='utils' class='mt0'>
    Utils
  </h2>

  
    <p>Utility functions and classes.</p>

  
</section></div>
          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='fulluuid'>
      fullUUID
    </h3>
    
    
  </div>
  

  <p>Converts UUID to full 128bit, lowercase format which should be used to compare UUID values.</p>

    <div class='pre p1 fill-light mt0'>fullUUID(uuid: <a href="#uuid">UUID</a>): <a href="#uuid">UUID</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>uuid</span> <code class='quiet'>(<a href="#uuid">UUID</a>)</code>
	    16bit, 32bit or 128bit UUID.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#uuid">UUID</a></code>:
        128bit lowercase UUID.

      
    
  

  

  

  

  

  

  

  
</section>

          
        
          
            <div class='keyline-top-not py2'><section class='py2 clearfix'>

  <h2 id='ble-error' class='mt0'>
    BLE Error
  </h2>

  
    <p>Types and classes related to BLE errors.</p>

  
</section></div>
          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='bleerror'>
      BleError
    </h3>
    
    
  </div>
  

  <p>BleError is an error class which is guaranteed to be thrown by all functions of this
library. It contains additional properties which help to identify problems in
platform independent way.</p>

    <div class='pre p1 fill-light mt0'>new BleError(nativeBleError: (NativeBleError | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), errorMessageMapping: <a href="#bleerrorcodemessagemapping">BleErrorCodeMessageMapping</a>)</div>
  
  
    <p>
      Extends
      
        <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Error">Error</a>
      
    </p>
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>nativeBleError</span> <code class='quiet'>((NativeBleError | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>errorMessageMapping</span> <code class='quiet'>(<a href="#bleerrorcodemessagemapping">BleErrorCodeMessageMapping</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='bleerrorerrorcode'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>errorCode</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Platform independent error code. Possible values are defined in <a href="#bleerrorcode">BleErrorCode</a>.</p>

    <div class='pre p1 fill-light mt0'>errorCode</div>
  
    <p>
      Type:
      $Values&#x3C;any>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerroratterrorcode'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>attErrorCode</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Platform independent error code related to ATT errors.</p>

    <div class='pre p1 fill-light mt0'>attErrorCode</div>
  
    <p>
      Type:
      $Values&#x3C;any>?
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorioserrorcode'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>iosErrorCode</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>iOS specific error code (if not an ATT error).</p>

    <div class='pre p1 fill-light mt0'>iosErrorCode</div>
  
    <p>
      Type:
      $Values&#x3C;any>?
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorandroiderrorcode'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>androidErrorCode</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Android specific error code (if not an ATT error).</p>

    <div class='pre p1 fill-light mt0'>androidErrorCode</div>
  
    <p>
      Type:
      $Values&#x3C;any>?
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorreason'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>reason</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Platform specific error message.</p>

    <div class='pre p1 fill-light mt0'>reason</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='bleerrorcode'>
      BleErrorCode
    </h3>
    
    
  </div>
  

  <p>Platform independent error code map adjusted to this library's use cases.</p>

    <div class='pre p1 fill-light mt0'>BleErrorCode</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Static Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='bleerrorcodeunknownerror'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>UnknownError</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>This error can be thrown when unexpected error occurred and in most cases it is related to implementation bug.
Original message is available in <a href="#bleerrorreason">reason</a> property.</p>

    <div class='pre p1 fill-light mt0'>UnknownError</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodebluetoothmanagerdestroyed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>BluetoothManagerDestroyed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Current promise failed to finish due to BleManager shutdown. It means that user called
<a href="#blemanagerdestroy">manager.destroy()</a> function before all operations completed.</p>

    <div class='pre p1 fill-light mt0'>BluetoothManagerDestroyed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodeoperationcancelled'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>OperationCancelled</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Promise was explicitly cancelled by a user with <a href="#blemanagercanceltransaction">manager.cancelTransaction()</a>
function call.</p>

    <div class='pre p1 fill-light mt0'>OperationCancelled</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodeoperationtimedout'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>OperationTimedOut</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Operation timed out and was cancelled.</p>

    <div class='pre p1 fill-light mt0'>OperationTimedOut</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodeoperationstartfailed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>OperationStartFailed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Native module couldn't start operation due to internal state, which doesn't allow to do that.</p>

    <div class='pre p1 fill-light mt0'>OperationStartFailed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodeinvalididentifiers'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>InvalidIdentifiers</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Invalid UUIDs or IDs were passed to API call.</p>

    <div class='pre p1 fill-light mt0'>InvalidIdentifiers</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodebluetoothunsupported'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>BluetoothUnsupported</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Bluetooth is not supported for this particular device. It may happen on iOS simulator for example.</p>

    <div class='pre p1 fill-light mt0'>BluetoothUnsupported</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodebluetoothunauthorized'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>BluetoothUnauthorized</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>There are no granted permissions which allow to use BLE functionality. On Android it may require
android.permission.ACCESS_COARSE_LOCATION permission or android.permission.ACCESS_FINE_LOCATION permission.</p>

    <div class='pre p1 fill-light mt0'>BluetoothUnauthorized</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodebluetoothpoweredoff'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>BluetoothPoweredOff</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>BLE is powered off on device. All previous operations and their state is invalidated.</p>

    <div class='pre p1 fill-light mt0'>BluetoothPoweredOff</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodebluetoothinunknownstate'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>BluetoothInUnknownState</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>BLE stack is in unspecified state.</p>

    <div class='pre p1 fill-light mt0'>BluetoothInUnknownState</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodebluetoothresetting'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>BluetoothResetting</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>BLE stack is resetting.</p>

    <div class='pre p1 fill-light mt0'>BluetoothResetting</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodebluetoothstatechangefailed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>BluetoothStateChangeFailed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>BLE state change failed.</p>

    <div class='pre p1 fill-light mt0'>BluetoothStateChangeFailed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodedeviceconnectionfailed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>DeviceConnectionFailed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Couldn't connect to specified device.</p>

    <div class='pre p1 fill-light mt0'>DeviceConnectionFailed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodedevicedisconnected'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>DeviceDisconnected</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Device was disconnected.</p>

    <div class='pre p1 fill-light mt0'>DeviceDisconnected</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodedevicerssireadfailed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>DeviceRSSIReadFailed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Couldn't read RSSI from device.</p>

    <div class='pre p1 fill-light mt0'>DeviceRSSIReadFailed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodedevicealreadyconnected'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>DeviceAlreadyConnected</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Device is already connected. It is not allowed to connect twice to the same device.</p>

    <div class='pre p1 fill-light mt0'>DeviceAlreadyConnected</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodedevicenotfound'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>DeviceNotFound</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Couldn't find device with specified ID.</p>

    <div class='pre p1 fill-light mt0'>DeviceNotFound</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodedevicenotconnected'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>DeviceNotConnected</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Operation failed because device has to be connected to perform it.</p>

    <div class='pre p1 fill-light mt0'>DeviceNotConnected</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodedevicemtuchangefailed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>DeviceMTUChangeFailed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Device could not change MTU value.</p>

    <div class='pre p1 fill-light mt0'>DeviceMTUChangeFailed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodeservicesdiscoveryfailed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>ServicesDiscoveryFailed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Couldn't discover services for specified device.</p>

    <div class='pre p1 fill-light mt0'>ServicesDiscoveryFailed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodeincludedservicesdiscoveryfailed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>IncludedServicesDiscoveryFailed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Couldn't discover included services for specified service.</p>

    <div class='pre p1 fill-light mt0'>IncludedServicesDiscoveryFailed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodeservicenotfound'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>ServiceNotFound</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Service with specified ID or UUID couldn't be found. User may need to call
<a href="#blemanagerdiscoverallservicesandcharacteristicsfordevice">manager.discoverAllServicesAndCharacteristicsForDevice</a>
to cache them.</p>

    <div class='pre p1 fill-light mt0'>ServiceNotFound</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodeservicesnotdiscovered'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>ServicesNotDiscovered</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Services were not discovered. User may need to call
<a href="#blemanagerdiscoverallservicesandcharacteristicsfordevice">manager.discoverAllServicesAndCharacteristicsForDevice</a>
to cache them.</p>

    <div class='pre p1 fill-light mt0'>ServicesNotDiscovered</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodecharacteristicsdiscoveryfailed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>CharacteristicsDiscoveryFailed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Couldn't discover characteristics for specified service.</p>

    <div class='pre p1 fill-light mt0'>CharacteristicsDiscoveryFailed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodecharacteristicwritefailed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>CharacteristicWriteFailed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Couldn't write to specified characteristic. Make sure that
<a href="#characteristiciswritablewithresponse">characteristic.isWritableWithResponse</a>
or <a href="#characteristiciswritablewithoutresponse">characteristic.isWritableWithoutResponse</a> is set to true.</p>

    <div class='pre p1 fill-light mt0'>CharacteristicWriteFailed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodecharacteristicreadfailed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>CharacteristicReadFailed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Couldn't read from specified characteristic. Make sure that
<a href="#characteristicisreadable">characteristic.isReadable</a> is set to true.</p>

    <div class='pre p1 fill-light mt0'>CharacteristicReadFailed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodecharacteristicnotifychangefailed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>CharacteristicNotifyChangeFailed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Couldn't set notification or indication for specified characteristic. Make sure that
<a href="#characteristicisnotifiable">characteristic.isNotifiable</a> or
<a href="#characteristicisindicatable">characteristic.isIndicatable</a> is set to true.</p>

    <div class='pre p1 fill-light mt0'>CharacteristicNotifyChangeFailed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodecharacteristicnotfound'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>CharacteristicNotFound</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Characteristic with specified ID or UUID couldn't be found. User may need to call
<a href="#blemanagerdiscoverallservicesandcharacteristicsfordevice">manager.discoverAllServicesAndCharacteristicsForDevice</a>
to cache them.</p>

    <div class='pre p1 fill-light mt0'>CharacteristicNotFound</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodecharacteristicsnotdiscovered'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>CharacteristicsNotDiscovered</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Characteristic were not discovered for specified service. User may need to call
<a href="#blemanagerdiscoverallservicesandcharacteristicsfordevice">manager.discoverAllServicesAndCharacteristicsForDevice</a>
to cache them.</p>

    <div class='pre p1 fill-light mt0'>CharacteristicsNotDiscovered</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodecharacteristicinvaliddataformat'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>CharacteristicInvalidDataFormat</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Invalid Base64 format was passed to characteristic API function call.</p>

    <div class='pre p1 fill-light mt0'>CharacteristicInvalidDataFormat</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodedescriptorsdiscoveryfailed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>DescriptorsDiscoveryFailed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Couldn't discover descriptor for specified characteristic.</p>

    <div class='pre p1 fill-light mt0'>DescriptorsDiscoveryFailed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodedescriptorwritefailed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>DescriptorWriteFailed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Couldn't write to specified descriptor.</p>

    <div class='pre p1 fill-light mt0'>DescriptorWriteFailed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodedescriptorreadfailed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>DescriptorReadFailed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Couldn't read from specified descriptor.</p>

    <div class='pre p1 fill-light mt0'>DescriptorReadFailed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodedescriptornotfound'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>DescriptorNotFound</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Couldn't find specified descriptor.</p>

    <div class='pre p1 fill-light mt0'>DescriptorNotFound</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodedescriptorsnotdiscovered'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>DescriptorsNotDiscovered</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Descriptors are not discovered for specified characteristic.</p>

    <div class='pre p1 fill-light mt0'>DescriptorsNotDiscovered</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodedescriptorinvaliddataformat'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>DescriptorInvalidDataFormat</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Invalid Base64 format was passed to descriptor API function call.</p>

    <div class='pre p1 fill-light mt0'>DescriptorInvalidDataFormat</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodedescriptorwritenotallowed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>DescriptorWriteNotAllowed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Issued a write to a descriptor, which is handled by OS.</p>

    <div class='pre p1 fill-light mt0'>DescriptorWriteNotAllowed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodescanstartfailed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>ScanStartFailed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Cannot start scanning operation.</p>

    <div class='pre p1 fill-light mt0'>ScanStartFailed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleerrorcodelocationservicesdisabled'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>LocationServicesDisabled</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Location services are disabled.</p>

    <div class='pre p1 fill-light mt0'>LocationServicesDisabled</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='bleerrorcodemessage'>
      BleErrorCodeMessage
    </h3>
    
    
  </div>
  

  <p>Mapping of error codes to error messages</p>

    <div class='pre p1 fill-light mt0'>BleErrorCodeMessage</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='bleatterrorcode'>
      BleATTErrorCode
    </h3>
    
    
  </div>
  

  <p>Error codes for ATT errors.</p>

    <div class='pre p1 fill-light mt0'>BleATTErrorCode</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Static Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='bleatterrorcodesuccess'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Success</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The ATT command or request successfully completed.</p>

    <div class='pre p1 fill-light mt0'>Success</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleatterrorcodeinvalidhandle'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>InvalidHandle</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The attribute handle is invalid on this device.</p>

    <div class='pre p1 fill-light mt0'>InvalidHandle</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleatterrorcodereadnotpermitted'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>ReadNotPermitted</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The attribute’s value cannot be read.</p>

    <div class='pre p1 fill-light mt0'>ReadNotPermitted</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleatterrorcodewritenotpermitted'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>WriteNotPermitted</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The attribute’s value cannot be written.</p>

    <div class='pre p1 fill-light mt0'>WriteNotPermitted</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleatterrorcodeinvalidpdu'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>InvalidPdu</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The attribute Protocol Data Unit (PDU) or “message” is invalid.</p>

    <div class='pre p1 fill-light mt0'>InvalidPdu</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleatterrorcodeinsufficientauthentication'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>InsufficientAuthentication</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The attribute requires authentication before its value can be read or written.</p>

    <div class='pre p1 fill-light mt0'>InsufficientAuthentication</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleatterrorcoderequestnotsupported'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>RequestNotSupported</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The attribute server does not support the request received by the client.</p>

    <div class='pre p1 fill-light mt0'>RequestNotSupported</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleatterrorcodeinvalidoffset'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>InvalidOffset</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The specified offset value was past the end of the attribute’s value.</p>

    <div class='pre p1 fill-light mt0'>InvalidOffset</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleatterrorcodeinsufficientauthorization'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>InsufficientAuthorization</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The attribute requires authorization before its value can be read or written.</p>

    <div class='pre p1 fill-light mt0'>InsufficientAuthorization</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleatterrorcodepreparequeuefull'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>PrepareQueueFull</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The prepare queue is full, because too many prepare write requests have been queued.</p>

    <div class='pre p1 fill-light mt0'>PrepareQueueFull</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleatterrorcodeattributenotfound'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>AttributeNotFound</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The attribute is not found within the specified attribute handle range.</p>

    <div class='pre p1 fill-light mt0'>AttributeNotFound</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleatterrorcodeattributenotlong'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>AttributeNotLong</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The attribute cannot be read or written using the ATT read blob request.</p>

    <div class='pre p1 fill-light mt0'>AttributeNotLong</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleatterrorcodeinsufficientencryptionkeysize'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>InsufficientEncryptionKeySize</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The encryption key size used for encrypting this link is insufficient.</p>

    <div class='pre p1 fill-light mt0'>InsufficientEncryptionKeySize</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleatterrorcodeinvalidattributevaluelength'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>InvalidAttributeValueLength</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The length of the attribute’s value is invalid for the intended operation.</p>

    <div class='pre p1 fill-light mt0'>InvalidAttributeValueLength</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleatterrorcodeunlikelyerror'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>UnlikelyError</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The ATT request has encountered an unlikely error and therefore could not be completed.</p>

    <div class='pre p1 fill-light mt0'>UnlikelyError</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleatterrorcodeinsufficientencryption'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>InsufficientEncryption</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The attribute requires encryption before its value can be read or written.</p>

    <div class='pre p1 fill-light mt0'>InsufficientEncryption</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleatterrorcodeunsupportedgrouptype'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>UnsupportedGroupType</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The attribute type is not a supported grouping attribute as defined by a higher-layer specification.</p>

    <div class='pre p1 fill-light mt0'>UnsupportedGroupType</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleatterrorcodeinsufficientresources'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>InsufficientResources</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Resources are insufficient to complete the ATT request.</p>

    <div class='pre p1 fill-light mt0'>InsufficientResources</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='bleandroiderrorcode'>
      BleAndroidErrorCode
    </h3>
    
    
  </div>
  

  <p>Android specific error codes.</p>

    <div class='pre p1 fill-light mt0'>BleAndroidErrorCode</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Static Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='bleandroiderrorcodenoresources'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>NoResources</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The device has insufficient resources to complete the intended operation.</p>

    <div class='pre p1 fill-light mt0'>NoResources</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleandroiderrorcodeinternalerror'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>InternalError</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Internal error occurred which may happen due to implementation error in BLE stack.</p>

    <div class='pre p1 fill-light mt0'>InternalError</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleandroiderrorcodewrongstate'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>WrongState</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>BLE stack implementation entered illegal state and operation couldn't complete.</p>

    <div class='pre p1 fill-light mt0'>WrongState</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleandroiderrorcodedbfull'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>DbFull</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>BLE stack didn't allocate sufficient memory buffer for internal caches.</p>

    <div class='pre p1 fill-light mt0'>DbFull</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleandroiderrorcodebusy'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Busy</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Maximum number of pending operations was exceeded.</p>

    <div class='pre p1 fill-light mt0'>Busy</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleandroiderrorcodeerror'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Error</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Generic BLE stack error.</p>

    <div class='pre p1 fill-light mt0'>Error</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleandroiderrorcodecmdstarted'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>CmdStarted</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Command is already queued up in GATT.</p>

    <div class='pre p1 fill-light mt0'>CmdStarted</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleandroiderrorcodeillegalparameter'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>IllegalParameter</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Illegal parameter was passed to internal BLE stack function.</p>

    <div class='pre p1 fill-light mt0'>IllegalParameter</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleandroiderrorcodepending'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Pending</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Operation is pending.</p>

    <div class='pre p1 fill-light mt0'>Pending</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleandroiderrorcodeauthfail'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>AuthFail</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Authorization failed before performing read or write operation.</p>

    <div class='pre p1 fill-light mt0'>AuthFail</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleandroiderrorcodemore'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>More</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>More cache entries were loaded then expected.</p>

    <div class='pre p1 fill-light mt0'>More</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleandroiderrorcodeinvalidcfg'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>InvalidCfg</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Invalid configuration</p>

    <div class='pre p1 fill-light mt0'>InvalidCfg</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleandroiderrorcodeservicestarted'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>ServiceStarted</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>GATT service already started.</p>

    <div class='pre p1 fill-light mt0'>ServiceStarted</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleandroiderrorcodeencrypednomitm'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>EncrypedNoMitm</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>GATT link is encrypted but prone to man in the middle attacks.</p>

    <div class='pre p1 fill-light mt0'>EncrypedNoMitm</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleandroiderrorcodenotencrypted'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>NotEncrypted</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>GATT link is not encrypted.</p>

    <div class='pre p1 fill-light mt0'>NotEncrypted</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleandroiderrorcodecongested'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Congested</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>ATT command was sent but channel is congested.</p>

    <div class='pre p1 fill-light mt0'>Congested</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='bleioserrorcode'>
      BleIOSErrorCode
    </h3>
    
    
  </div>
  

  <p>iOS specific error codes.</p>

    <div class='pre p1 fill-light mt0'>BleIOSErrorCode</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Static Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='bleioserrorcodeunknown'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Unknown</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>An unknown error occurred.</p>

    <div class='pre p1 fill-light mt0'>Unknown</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleioserrorcodeinvalidparameters'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>InvalidParameters</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The specified parameters are invalid.</p>

    <div class='pre p1 fill-light mt0'>InvalidParameters</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleioserrorcodeinvalidhandle'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>InvalidHandle</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The specified attribute handle is invalid.</p>

    <div class='pre p1 fill-light mt0'>InvalidHandle</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleioserrorcodenotconnected'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>NotConnected</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The device is not currently connected.</p>

    <div class='pre p1 fill-light mt0'>NotConnected</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleioserrorcodeoutofspace'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>OutOfSpace</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The device has run out of space to complete the intended operation.</p>

    <div class='pre p1 fill-light mt0'>OutOfSpace</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleioserrorcodeoperationcancelled'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>OperationCancelled</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The operation is canceled.</p>

    <div class='pre p1 fill-light mt0'>OperationCancelled</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleioserrorcodeconnectiontimeout'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>ConnectionTimeout</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The connection timed out.</p>

    <div class='pre p1 fill-light mt0'>ConnectionTimeout</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleioserrorcodeperipheraldisconnected'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>PeripheralDisconnected</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The peripheral disconnected.</p>

    <div class='pre p1 fill-light mt0'>PeripheralDisconnected</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleioserrorcodeuuidnotallowed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>UuidNotAllowed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The specified UUID is not permitted.</p>

    <div class='pre p1 fill-light mt0'>UuidNotAllowed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleioserrorcodealreadyadvertising'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>AlreadyAdvertising</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The peripheral is already advertising.</p>

    <div class='pre p1 fill-light mt0'>AlreadyAdvertising</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleioserrorcodeconnectionfailed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>ConnectionFailed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The connection failed.</p>

    <div class='pre p1 fill-light mt0'>ConnectionFailed</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleioserrorcodeconnectionlimitreached'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>ConnectionLimitReached</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The device already has the maximum number of connections.</p>

    <div class='pre p1 fill-light mt0'>ConnectionLimitReached</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bleioserrorcodeunknowndevice'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>UnknownDevice</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Unknown device.</p>

    <div class='pre p1 fill-light mt0'>UnknownDevice</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  

  
</section>

          
        
          
            <div class='keyline-top-not py2'><section class='py2 clearfix'>

  <h2 id='flow-types' class='mt0'>
    Flow Types
  </h2>

  
    <p>All Flow aliases and Flow types used in this library.</p>

  
</section></div>
          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='loglevel'>
      LogLevel
    </h3>
    
    
  </div>
  

  <p>Native module logging log level. By default it is set to None.</p>

    <div class='pre p1 fill-light mt0'>LogLevel</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Static Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='loglevelnone'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>None</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Logging in native module is disabled</p>

    <div class='pre p1 fill-light mt0'>None</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='loglevelverbose'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Verbose</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>All logs in native module are shown</p>

    <div class='pre p1 fill-light mt0'>Verbose</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='logleveldebug'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Debug</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Only debug logs and of higher importance are shown in native module.</p>

    <div class='pre p1 fill-light mt0'>Debug</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='loglevelinfo'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Info</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Only info logs and of higher importance are shown in native module.</p>

    <div class='pre p1 fill-light mt0'>Info</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='loglevelwarning'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Warning</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Only warning logs and of higher importance are shown in native module.</p>

    <div class='pre p1 fill-light mt0'>Warning</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='loglevelerror'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Error</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Only error logs and of higher importance are shown in native module.</p>

    <div class='pre p1 fill-light mt0'>Error</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='connectionpriority'>
      ConnectionPriority
    </h3>
    
    
  </div>
  

  <p>Connection priority of BLE link determining the balance between power consumption and data throughput.</p>

    <div class='pre p1 fill-light mt0'>ConnectionPriority</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Static Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='connectionprioritybalanced'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Balanced</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Default, recommended option balanced between power consumption and data throughput.</p>

    <div class='pre p1 fill-light mt0'>Balanced</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='connectionpriorityhigh'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>High</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>High priority, low latency connection, which increases transfer speed at the expense of power consumption.</p>

    <div class='pre p1 fill-light mt0'>High</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='connectionprioritylowpower'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>LowPower</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Low power, reduced data rate connection setup.</p>

    <div class='pre p1 fill-light mt0'>LowPower</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='state'>
      State
    </h3>
    
    
  </div>
  

  <p>Device Bluetooth Low Energy state. It's keys are used to check <a href="#blemanagerstate">#blemanagerstate</a> values
received by <a href="#blemanager">BleManager</a></p>

    <div class='pre p1 fill-light mt0'>State</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Static Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='stateunknown'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Unknown</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The current state of the manager is unknown; an update is imminent.</p>

    <div class='pre p1 fill-light mt0'>Unknown</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='stateresetting'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Resetting</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The connection with the system service was momentarily lost; an update is imminent.</p>

    <div class='pre p1 fill-light mt0'>Resetting</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='stateunsupported'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Unsupported</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The platform does not support Bluetooth low energy.</p>

    <div class='pre p1 fill-light mt0'>Unsupported</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='stateunauthorized'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Unauthorized</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>The app is not authorized to use Bluetooth low energy.</p>

    <div class='pre p1 fill-light mt0'>Unauthorized</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='statepoweredoff'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>PoweredOff</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Bluetooth is currently powered off.</p>

    <div class='pre p1 fill-light mt0'>PoweredOff</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='statepoweredon'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>PoweredOn</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Bluetooth is currently powered on and available to use.</p>

    <div class='pre p1 fill-light mt0'>PoweredOn</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='bleerrorcodemessagemapping'>
      BleErrorCodeMessageMapping
    </h3>
    
    
  </div>
  

  <p>Type of error code mapping table</p>

    <div class='pre p1 fill-light mt0'>BleErrorCodeMessageMapping</div>
  
    <p>
      Type:
      {}
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='blemanageroptions'>
      BleManagerOptions
    </h3>
    
    
  </div>
  

  <p>Options which can be passed to when creating BLE Manager</p>

    <div class='pre p1 fill-light mt0'>BleManagerOptions</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='blemanageroptionsrestorestateidentifier'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>restoreStateIdentifier</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>BLE State restoration identifier used to restore state.</p>

    <div class='pre p1 fill-light mt0'>restoreStateIdentifier</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanageroptionsrestorestatefunction'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>restoreStateFunction(restoredState)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Optional function which is used to properly restore state of your BLE Manager. Callback
is emitted in the beginning of BleManager creation and optional <a href="BleRestoreState">BleRestoreState</a>
is passed. When value is <code>null</code> application is launching for the first time, otherwise
it contains saved state which may be used by developer to continue working with
connected peripherals.</p>

    <div class='pre p1 fill-light mt0'>restoreStateFunction(restoredState: <a href="#blerestoredstate">BleRestoredState</a>?): void</div>
  
    <p>
      Type:
      function (restoredState: <a href="#blerestoredstate">BleRestoredState</a>?): void
    </p>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>restoredState</span> <code class='quiet'>(<a href="#blerestoredstate">BleRestoredState</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>void</code>
    
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='blemanageroptionserrorcodestomessagesmapping'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>errorCodesToMessagesMapping</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Optional mapping of error codes to error messages. Uses <a href="#bleerrorcodemessage">BleErrorCodeMessage</a>
by default.</p>
<p>To override logging UUIDs or MAC adresses in error messages copy the original object
and overwrite values of interest to you.</p>

    <div class='pre p1 fill-light mt0'>errorCodesToMessagesMapping</div>
  
    <p>
      Type:
      <a href="#bleerrorcodemessagemapping">BleErrorCodeMessageMapping</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='blerestoredstate'>
      BleRestoredState
    </h3>
    
    
  </div>
  

  <p>Object representing information about restored BLE state after application relaunch.</p>

    <div class='pre p1 fill-light mt0'>BleRestoredState</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='blerestoredstateconnectedperipherals'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>connectedPeripherals</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>List of connected devices after state restoration.</p>

    <div class='pre p1 fill-light mt0'>connectedPeripherals</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#device">Device</a>>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='scanoptions'>
      ScanOptions
    </h3>
    
    
  </div>
  

  <p>Options which can be passed to scanning function</p>

    <div class='pre p1 fill-light mt0'>ScanOptions</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='scanoptionsallowduplicates'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>allowDuplicates</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>By allowing duplicates scanning records are received more frequently [iOS only]</p>

    <div class='pre p1 fill-light mt0'>allowDuplicates</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='scanoptionsscanmode'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>scanMode</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Scan mode for Bluetooth LE scan [Android only]</p>

    <div class='pre p1 fill-light mt0'>scanMode</div>
  
    <p>
      Type:
      $Values&#x3C;any>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='scanoptionscallbacktype'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>callbackType</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Scan callback type for Bluetooth LE scan [Android only]</p>

    <div class='pre p1 fill-light mt0'>callbackType</div>
  
    <p>
      Type:
      $Values&#x3C;any>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='scanoptionslegacyscan'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>legacyScan</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Use legacyScan (default true) [Android only]
<a href="https://developer.android.com/reference/android/bluetooth/le/ScanSettings.Builder#setLegacy(boolean">https://developer.android.com/reference/android/bluetooth/le/ScanSettings.Builder#setLegacy(boolean</a>)</p>

    <div class='pre p1 fill-light mt0'>legacyScan</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='scancallbacktype'>
      ScanCallbackType
    </h3>
    
    
  </div>
  

  <p>Scan callback type for Bluetooth LE scan.</p>

    <div class='pre p1 fill-light mt0'>ScanCallbackType</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Static Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='scancallbacktypeallmatches'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>AllMatches</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Trigger a callback for every Bluetooth advertisement found that matches the filter criteria.
If no filter is active, all advertisement packets are reported. [default value]</p>

    <div class='pre p1 fill-light mt0'>AllMatches</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='scancallbacktypefirstmatch'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>FirstMatch</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>A result callback is only triggered for the first advertisement packet received that matches
the filter criteria.</p>

    <div class='pre p1 fill-light mt0'>FirstMatch</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='scancallbacktypematchlost'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>MatchLost</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Receive a callback when advertisements are no longer received from a device that has been
previously reported by a first match callback.</p>

    <div class='pre p1 fill-light mt0'>MatchLost</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='scanmode'>
      ScanMode
    </h3>
    
    
  </div>
  

  <p>Scan mode for Bluetooth LE scan.</p>

    <div class='pre p1 fill-light mt0'>ScanMode</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Static Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='scanmodeopportunistic'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Opportunistic</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>A special Bluetooth LE scan mode. Applications using this scan mode will passively listen for
other scan results without starting BLE scans themselves.</p>

    <div class='pre p1 fill-light mt0'>Opportunistic</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='scanmodelowpower'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>LowPower</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Perform Bluetooth LE scan in low power mode. This is the default scan mode as it consumes the
least power. [default value]</p>

    <div class='pre p1 fill-light mt0'>LowPower</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='scanmodebalanced'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>Balanced</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Perform Bluetooth LE scan in balanced power mode. Scan results are returned at a rate that
provides a good trade-off between scan frequency and power consumption.</p>

    <div class='pre p1 fill-light mt0'>Balanced</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='scanmodelowlatency'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>LowLatency</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Scan using highest duty cycle. It's recommended to only use this mode when the application is
running in the foreground.</p>

    <div class='pre p1 fill-light mt0'>LowLatency</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='connectionoptions'>
      ConnectionOptions
    </h3>
    
    
  </div>
  

  <p>Connection specific options to be passed before connection happen. [Not used]</p>

    <div class='pre p1 fill-light mt0'>ConnectionOptions</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='connectionoptionsautoconnect'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>autoConnect</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Whether to directly connect to the remote device (false) or to automatically connect as soon as the remote device
becomes available (true). [Android only]</p>

    <div class='pre p1 fill-light mt0'>autoConnect</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='connectionoptionsrequestmtu'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>requestMTU</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Whether MTU size will be negotiated to this value. It is not guaranteed to get it after connection is successful.</p>

    <div class='pre p1 fill-light mt0'>requestMTU</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='connectionoptionsrefreshgatt'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>refreshGatt</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Whether action will be taken to reset services cache. This option may be useful when a peripheral's firmware was
updated and it's services/characteristics were added/removed/altered. [Android only]
<a href="https://stackoverflow.com/questions/22596951/how-to-programmatically-force-bluetooth-low-energy-service-discovery-on-android">https://stackoverflow.com/questions/22596951/how-to-programmatically-force-bluetooth-low-energy-service-discovery-on-android</a></p>

    <div class='pre p1 fill-light mt0'>refreshGatt</div>
  
    <p>
      Type:
      <a href="#refreshgattmoment">RefreshGattMoment</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='connectionoptionstimeout'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>timeout</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Number of milliseconds after connection is automatically timed out. In case of race condition were connection is
established right after timeout event, device will be disconnected immediately. Time out may happen earlier then
specified due to OS specific behavior.</p>

    <div class='pre p1 fill-light mt0'>timeout</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='deviceid'>
      DeviceId
    </h3>
    
    
  </div>
  

  <p>Bluetooth device id.</p>

    <div class='pre p1 fill-light mt0'>DeviceId</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='identifier'>
      Identifier
    </h3>
    
    
  </div>
  

  <p>Unique identifier for BLE objects.</p>

    <div class='pre p1 fill-light mt0'>Identifier</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='uuid'>
      UUID
    </h3>
    
    
  </div>
  

  <p>Bluetooth UUID</p>

    <div class='pre p1 fill-light mt0'>UUID</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='transactionid'>
      TransactionId
    </h3>
    
    
  </div>
  

  <p>Transaction identifier. All transaction identifiers in numeric form are reserved for internal use.</p>

    <div class='pre p1 fill-light mt0'>TransactionId</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='subscription'>
      Subscription
    </h3>
    
    
  </div>
  

  <p>Subscription</p>

    <div class='pre p1 fill-light mt0'>Subscription</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='base64'>
      Base64
    </h3>
    
    
  </div>
  

  <p>Base64 value</p>

    <div class='pre p1 fill-light mt0'>Base64</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='refreshgattmoment'>
      RefreshGattMoment
    </h3>
    
    
  </div>
  

  <p>[Android only] ConnectionOptions parameter to describe when to call BluetoothGatt.refresh()</p>

    <div class='pre p1 fill-light mt0'>RefreshGattMoment</div>
  
    <p>
      Type:
      <code>"OnConnected"</code>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  

  
</section>

          
        
      </div>
    </div>
  <script src='assets/anchor.js'></script>
  <script src='assets/split.js'></script>
  <script src='assets/site.js'></script>
</body>
</html>
